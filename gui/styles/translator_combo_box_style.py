TRANSLATOR_COMBO_BOX_STYLE = """
QComboBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 10px 40px 10px 15px;
    color: #f0f6fc;
    font-size: 13px;
    min-width: 180px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #484f58;
    background-color: #30363d;
}

QComboBox:focus {
    border: 2px solid #58a6ff;
    outline: none;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 14px;
    background-color: #30363d;
    margin-right: 6px;
    margin-top: 1px;
    margin-bottom: 1px;
}

QComboBox::drop-down:hover {
    background-color: #484f58;
}

QComboBox::down-arrow {
    image: url(gui/assets/icons/dropdown_arrow.png);
    width: 12px;
    height: 12px;
    margin: 0px;
    background: transparent;
    border: none;
}

QComboBox QAbstractItemView {
    background-color: transparent;
    border: none;
    border-radius: 10px;
    padding: 8px 12px;
    margin: 0px;
    alternate-background-color: transparent;
    color: #f0f6fc;
    font-size: 13px;
    font-weight: normal;
    min-width: 240px;
    selection-background-color: transparent;
    selection-color: #f0f6fc;
    outline: none;
    gridline-color: transparent;
    show-decoration-selected: 1;
}

QComboBox QAbstractItemView::viewport {
    background-color: #0d1117;
    border: none;
    margin: 0px;
    padding: 0px;
}

QComboBox QAbstractItemView::item {
    padding: 12px 16px;
    border: none;
    min-height: 28px;
    border-radius: 5px;
    margin: 6px 12px;
    background-color: transparent;
    color: #f0f6fc;
    font-size: 13px;
    font-weight: normal;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #21262d;
    border-radius: 5px;
    color: #ffffff;
    border: none;
    font-weight: normal;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #238636;
    border-radius: 5px;
    color: #ffffff;
    font-weight: normal;
}

QComboBox QAbstractItemView::item:selected:hover {
    background-color: #2ea043;
    border-radius: 5px;
    color: #ffffff;
    font-weight: normal;
}
"""

TRANSLATOR_COMBO_BOX_POPUP_STYLE = "QWidget { background-color: #0d1117; border-radius: 10px; }"
TRANSLATOR_COMBO_BOX_LIST_STYLE = "QListView { background-color: #0d1117; border: none; border-radius: 10px; font-weight: normal; }"
