from PyQt6.QtWidgets import QTextEdit, QScrollArea, QWidget, QVBoxLayout
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor, QTextCharFormat, QColor
from ...styles.log_widget_style import LOG_WIDGET_STYLE
import datetime
import logging


class LogWidget(QTextEdit):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()
        self.add_welcome_message()
    
    def setup_ui(self):
        self.setReadOnly(True)
        self.setMinimumHeight(150)

        # Set font - use monospace for better log readability
        font = QFont("Consolas", 11)
        if not font.exactMatch():
            font = QFont("Monaco", 11)
            if not font.exactMatch():
                font = QFont("Courier New", 11)
        self.setFont(font)
    
    def apply_styles(self):
        self.setStyleSheet(LOG_WIDGET_STYLE)
    
    def add_welcome_message(self):
        """Add welcome message to indicate real logging is active"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        welcome_msg = f"[{timestamp}] Excel Translator logging system initialized"
        # Make this green since it's a regular log message
        self.append(f'<span style="color: #51cf66;">{welcome_msg}</span>')
        self.scroll_to_bottom()
    
    def add_log(self, message, level="INFO"):
        """Add a new log message with timestamp and level"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Handle status messages differently (no level prefix for status indicators)
        if level.startswith("status_"):
            formatted_message = f"[{timestamp}] {message}"
        else:
            formatted_message = f"[{timestamp}] {level.upper()}: {message}"

        # Apply color based on log level
        if level.upper() == "ERROR":
            self.append(f'<span style="color: #ff6b6b;">{formatted_message}</span>')
        elif level.upper() == "WARNING":
            self.append(f'<span style="color: #ffa500;">{formatted_message}</span>')
        elif level.upper() == "SUCCESS":
            self.append(f'<span style="color: #51cf66;">{formatted_message}</span>')
        # All workflow status messages (with ======) should be GRAY
        elif level.startswith("status_"):
            self.append(f'<span style="color: #8b949e; font-weight: bold;">{formatted_message}</span>')
        else:
            # All other regular logs should be GREEN
            self.append(f'<span style="color: #51cf66;">{formatted_message}</span>')

        self.scroll_to_bottom()
    
    def add_business_log(self, message, component="APP"):
        """Add a business logic log message"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {component}: {message}"
        self.append(formatted_message)
        self.scroll_to_bottom()
    
    def scroll_to_bottom(self):
        """Auto-scroll to bottom to show latest logs"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.setTextCursor(cursor)
    
    def clear_logs(self):
        """Clear all log messages"""
        self.clear()
