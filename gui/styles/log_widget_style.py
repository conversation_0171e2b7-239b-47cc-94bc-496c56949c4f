LOG_WIDGET_STYLE = """
QTextEdit {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    padding: 12px;
    selection-background-color: #21262d;
    font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    font-weight: normal;
    line-height: 1.4;
}

QTextEdit QScrollArea {
    background-color: #0d1117;
}

QTextEdit QWidget {
    background-color: #0d1117;
}

QScrollBar:vertical {
    background-color: #161b22;
    width: 12px;
    border-radius: 3px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #30363d;
    border-radius: 3px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #484f58;
    border-radius: 3px;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}
"""
