"""
ComboBox Styling

Styles for dropdown/combobox components in the Translation Application.
"""

# Default ComboBox Style
COMBOBOX_STYLE = """
QComboBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 10px 40px 10px 15px;
    color: #f0f6fc;
    font-size: 13px;
    min-width: 180px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #484f58;
    background-color: #30363d;
}

QComboBox:focus {
    border: 2px solid #58a6ff;
    outline: none;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 28px;
    border: none;
    border-radius: 8px;
    background-color: transparent;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #666666;
    margin: 0px 2px;
}

QComboBox::down-arrow:hover {
    border-top-color: #f0f6fc;
}

QComboBox QAbstractItemView {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 5px;
    color: #f0f6fc;
    selection-background-color: #484f58;
    selection-color: #f0f6fc;
    outline: none;
    margin-left: 5px;
    margin-right: 5px;
}

QComboBox QAbstractItemView::item {
    padding: 10px 15px;
    border: none;
    margin: 2px 0px;
    border-radius: 4px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #30363d;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #484f58;
}
"""

SOURCE_LANGUAGE_STYLE = COMBOBOX_STYLE
TARGET_LANGUAGE_STYLE = COMBOBOX_STYLE
TRANSLATOR_STYLE = COMBOBOX_STYLE

# Translator ComboBox Component Styles
TRANSLATOR_COMBOBOX_COMPONENT_STYLE = """
QComboBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 10px 40px 10px 15px;
    color: #f0f6fc;
    font-size: 13px;
    min-width: 180px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #484f58;
    background-color: #30363d;
}

QComboBox:focus {
    border: 2px solid #58a6ff;
    outline: none;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 28px;
    border: none;
    border-radius: 8px;
    background-color: transparent;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #666666;
    margin: 0px 2px;
}

QComboBox::down-arrow:hover {
    border-top-color: #f0f6fc;
}

QComboBox QAbstractItemView {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 5px;
    color: #f0f6fc;
    selection-background-color: #484f58;
    selection-color: #f0f6fc;
    outline: none;
    margin-left: 5px;
    margin-right: 5px;
}

QComboBox QAbstractItemView::item {
    padding: 10px 15px;
    border: none;
    margin: 2px 0px;
    border-radius: 4px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #30363d;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #484f58;
}
"""
