from PyQt6.QtWidgets import QComboBox
from PyQt6.QtCore import Qt
from ...styles.translator_combo_box_style import (
    TRANSLATOR_COMBO_BOX_STYLE,
    TRANSLATOR_COMBO_BOX_POPUP_STYLE,
    TRANSLATOR_COMBO_BOX_LIST_STYLE
)
from ...managers.localization_manager import get_localization_manager, tr


class SourceLanguageComboBox(QComboBox):
    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_texts)

    def setup_ui(self):
        self.setFixedHeight(35)
        self.populate_languages()

    def populate_languages(self):
        """Populate the combo box with localized language options."""
        self.clear()
        languages = [
            tr("auto_detect"),
            tr("english"),
            tr("japanese"),
            tr("vietnamese")
        ]
        self.addItems(languages)
        self.setCurrentIndex(0)
    
    def apply_styles(self):
        self.setStyleSheet(TRANSLATOR_COMBO_BOX_STYLE)
        popup = self.view().window()
        popup.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        popup.setStyleSheet(TRANSLATOR_COMBO_BOX_POPUP_STYLE)
        self.view().setContentsMargins(0, 0, 0, 0)
        self.view().setFrameStyle(0)
        self.view().setStyleSheet(TRANSLATOR_COMBO_BOX_LIST_STYLE)

    def update_texts(self):
        """Update combo box items when language changes."""
        current_index = self.currentIndex()
        self.populate_languages()
        # Restore selection if valid
        if 0 <= current_index < self.count():
            self.setCurrentIndex(current_index)
