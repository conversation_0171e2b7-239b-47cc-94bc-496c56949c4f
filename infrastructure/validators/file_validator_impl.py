"""
File validator implementation.
"""

from typing import List
from pathlib import Path

from interfaces.validators.validation_interface import FileValidationInterface


class FileValidatorImpl(FileValidationInterface):
    """Implementation of file validation interface."""
    
    def __init__(self):
        """Initialize the file validator."""
        self.errors = []
        self.warnings = []
    
    def validate(self, data) -> bool:
        """Validate file data."""
        if isinstance(data, (str, Path)):
            return self.validate_file_path(Path(data))
        return False
    
    def validate_file_path(self, file_path: Path) -> bool:
        """Validate file path."""
        self.errors.clear()
        self.warnings.clear()
        
        try:
            if not file_path.exists():
                self.errors.append(f"File does not exist: {file_path}")
                return False
            
            if not file_path.is_file():
                self.errors.append(f"Path is not a file: {file_path}")
                return False
            
            if file_path.stat().st_size == 0:
                self.warnings.append(f"File is empty: {file_path}")
            
            return True
            
        except Exception as e:
            self.errors.append(f"Error validating file path: {str(e)}")
            return False
    
    def validate_file_size(self, file_path: Path, max_size_mb: float) -> bool:
        """Validate file size."""
        try:
            if not file_path.exists():
                self.errors.append(f"File does not exist: {file_path}")
                return False
            
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            
            if file_size_mb > max_size_mb:
                self.errors.append(f"File too large: {file_size_mb:.1f}MB > {max_size_mb}MB")
                return False
            
            if file_size_mb > max_size_mb * 0.8:
                self.warnings.append(f"File is large: {file_size_mb:.1f}MB")
            
            return True
            
        except Exception as e:
            self.errors.append(f"Error validating file size: {str(e)}")
            return False
    
    def validate_file_format(self, file_path: Path, allowed_formats: List[str]) -> bool:
        """Validate file format."""
        try:
            file_extension = file_path.suffix.lower()
            
            if file_extension not in allowed_formats:
                self.errors.append(f"Unsupported file format: {file_extension}")
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Error validating file format: {str(e)}")
            return False
    
    def get_validation_errors(self) -> List[str]:
        """Get validation error messages."""
        return self.errors.copy()
    
    def get_validation_warnings(self) -> List[str]:
        """Get validation warning messages."""
        return self.warnings.copy()
    
    def reset_validation_state(self) -> None:
        """Reset validation state."""
        self.errors.clear()
        self.warnings.clear()
