"""
Theme Manager for handling application styling and dark theme setup.
"""

from PyQt6.QtGui import QPalette, QColor


class ThemeManager:
    """Manages application themes and styling."""
    
    @staticmethod
    def apply_styles(window):
        """Apply application styles to the window."""
        from ..styles.application_style import APPLICATION_STYLE
        window.setStyleSheet(APPLICATION_STYLE)
    
    @staticmethod
    def setup_dark_palette(window):
        """Configure dark color palette for the window."""
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, QColor(13, 17, 23))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(240, 246, 252))
        palette.setColor(QPalette.ColorRole.Base, QColor(13, 17, 23))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(22, 27, 34))
        palette.setColor(QPalette.ColorRole.Text, QColor(240, 246, 252))
        palette.setColor(QPalette.ColorRole.Button, QColor(33, 38, 45))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(240, 246, 252))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(22, 27, 34))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(240, 246, 252))
        window.setPalette(palette)
    
    @staticmethod
    def setup_platform_dark_theme():
        """Setup platform-specific dark theme settings."""
        try:
            import platform
            system = platform.system()

            if system == "Windows":
                ThemeManager._setup_windows_dark_theme()
            elif system == "Darwin":
                ThemeManager._setup_macos_dark_theme()
        except Exception:
            pass
    
    @staticmethod
    def _setup_windows_dark_theme():
        """Setup Windows-specific dark theme."""
        try:
            import ctypes
            # Note: This requires the window to be created first
            # Will be called from the window after it's initialized
            pass
        except Exception:
            pass
    
    @staticmethod
    def setup_windows_dark_theme_for_window(window):
        """Setup Windows-specific dark theme for a specific window."""
        try:
            import ctypes
            hwnd = int(window.winId())
            DWMWA_USE_IMMERSIVE_DARK_MODE = 20
            value = ctypes.c_int(1)
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE,
                ctypes.byref(value), ctypes.sizeof(value)
            )
        except Exception:
            pass
    
    @staticmethod
    def _setup_macos_dark_theme():
        """Setup macOS-specific dark theme."""
        try:
            import objc
            from Foundation import NSBundle
            bundle = NSBundle.mainBundle()
            if bundle:
                info = bundle.localizedInfoDictionary() or bundle.infoDictionary()
                if info:
                    info['NSRequiresAquaSystemAppearance'] = False
        except Exception:
            pass
