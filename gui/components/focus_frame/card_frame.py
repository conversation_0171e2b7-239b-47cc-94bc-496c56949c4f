

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QFrame, QVBoxLayout, QLabel
from ...styles.card_frame_style import CARD_FRAME_STYLE


class CardFrame(QFrame):
    """A reusable card-style container with consistent styling"""
    
    def __init__(self, title=None, title_style="cardTitle"):
        super().__init__()
        self.title = title
        self.title_style = title_style
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        self.setObjectName("cardFrame")
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 16, 16, 16)
        self.layout.setSpacing(8)  # Professional spacing between title and content

        # Add title if provided
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setObjectName(self.title_style)
            self.layout.addWidget(self.title_label)
    
    def apply_styles(self):
        self.setStyleSheet(CARD_FRAME_STYLE)
    
    def add_widget(self, widget):
        """Add a widget to the card"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Add a layout to the card"""
        self.layout.addLayout(layout)
    
    def set_title(self, title):
        """Set or update the card title"""
        if hasattr(self, 'title_label'):
            self.title_label.setText(title)
        else:
            self.title_label = QLabel(title)
            self.title_label.setObjectName("cardTitle")
            self.layout.insertWidget(0, self.title_label)
        self.title = title
