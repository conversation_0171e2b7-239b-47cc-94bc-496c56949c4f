"""
Custom checkbox with tick sign for sheet selection
"""

from PyQt6.QtGui import <PERSON><PERSON><PERSON>ter, QPen, QFont
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import <PERSON><PERSON>heck<PERSON>ox
from ...styles.sheet_checkbox_style import SHEET_CHECKBOX_STYLE


class SheetCheckBox(QCheckBox):
    def __init__(self, text=""):
        super().__init__(text)
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        self.setFixedHeight(30)
    
    def apply_styles(self):
        self.setStyleSheet(SHEET_CHECKBOX_STYLE)
    
    def paintEvent(self, event):
        super().paintEvent(event)

        if self.isChecked():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            font = QFont()
            font.setFamily("Arial")
            font.setPixelSize(14)
            font.setBold(True)
            painter.setFont(font)

            pen = QPen(Qt.GlobalColor.black)
            painter.setPen(pen)

            tick_x = 8
            tick_y = 18
            painter.drawText(tick_x, tick_y, "✓")

            painter.end()
