"""
Google Cloud Translate API implementation - Simplified.
"""

import asyncio
from typing import List, Optional, Dict, Any
import time
import os

try:
    from google.cloud import translate_v2 as translate
    import requests
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    translate = None
    requests = None

from interfaces.services.translation_service_interface import TranslationServiceInterface
from domain.entities.translation import TranslationRequest, TranslationResult


class GoogleTranslator(TranslationServiceInterface):
    """Google Cloud Translate API implementation."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Google Translator."""
        self.api_key = api_key or os.getenv('GOOGLE_TRANSLATE_API_KEY')
        self.service_name = "Google Translate"
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Google Translate client."""
        try:
            if not GOOGLE_AVAILABLE:
                self.client = None
                print("Google Cloud Translate library not available")
                return

            if self.api_key and not self.api_key.startswith('your_') and self.api_key.strip():
                # Use REST API directly with API key
                self.client = "rest_api"  # Flag to indicate we're using REST API
                print(f"Google Translate REST API initialized with key: {self.api_key[:10]}...")
            else:
                self.client = None
                print(f"Google Translate client not initialized - invalid API key: {self.api_key}")
        except Exception as e:
            self.client = None
            print(f"Google Translate client initialization failed: {e}")

    def is_available(self) -> bool:
        """Check if the translator is available."""
        return GOOGLE_AVAILABLE and self.client is not None

    def _translate_with_rest_api(self, text: str, source_lang: str, target_lang: str) -> tuple:
        """Translate text using Google Translate REST API.

        Returns:
            tuple: (translated_text, detected_source_language)
        """
        try:
            import requests
            import json

            url = "https://translation.googleapis.com/language/translate/v2"

            params = {
                'key': self.api_key,
                'q': text,
                'target': target_lang
            }

            if source_lang and source_lang.lower() != 'auto':
                params['source'] = source_lang

            response = requests.post(url, data=params)
            response.raise_for_status()

            result = response.json()

            if 'data' in result and 'translations' in result['data']:
                translation_data = result['data']['translations'][0]
                translated_text = translation_data['translatedText']
                detected_language = translation_data.get('detectedSourceLanguage', source_lang)
                return translated_text, detected_language
            else:
                raise Exception(f"Unexpected response format: {result}")

        except Exception as e:
            print(f"REST API translation failed: {e}")
            return text, source_lang  # Return original text and source lang on failure

    def _translate_batch_with_rest_api(self, texts: List[str], source_lang: str, target_lang: str) -> List[tuple]:
        """
        Translate multiple texts in a single API call using Google Translate REST API.

        Returns:
            List[tuple]: List of (translated_text, detected_language) tuples
        """
        try:
            import requests
            import json

            url = "https://translation.googleapis.com/language/translate/v2"

            params = {
                'key': self.api_key,
                'q': texts,  # Send all texts at once
                'target': target_lang
            }

            if source_lang and source_lang.lower() != 'auto':
                params['source'] = source_lang

            response = requests.post(url, data=params)
            response.raise_for_status()

            result = response.json()

            if 'data' in result and 'translations' in result['data']:
                translations = result['data']['translations']
                batch_results = []

                for translation_data in translations:
                    translated_text = translation_data['translatedText']
                    detected_language = translation_data.get('detectedSourceLanguage', source_lang)
                    batch_results.append((translated_text, detected_language))

                return batch_results
            else:
                raise Exception(f"Unexpected response format: {result}")

        except Exception as e:
            # Fallback to individual translations if batch fails
            batch_results = []
            for text in texts:
                try:
                    translated_text, detected_language = self._translate_with_rest_api(text, source_lang, target_lang)
                    batch_results.append((translated_text, detected_language))
                except:
                    batch_results.append((text, source_lang))  # Return original if translation fails
            return batch_results

    async def translate_text(self, request: TranslationRequest) -> TranslationResult:
        """Translate a single text."""
        start_time = time.time()

        try:
            if not self.client:
                raise Exception("Google Translate client not initialized")

            # Use REST API for translation
            translated_text, detected_language = self._translate_with_rest_api(
                request.text,
                request.source_language,
                request.target_language
            )

            processing_time = time.time() - start_time

            # Use detected language if available, otherwise use request source language
            if not detected_language or detected_language == request.source_language:
                detected_language = request.source_language
            
            return TranslationResult(
                original_text=request.text,
                translated_text=translated_text,
                source_language=detected_language,
                target_language=request.target_language,
                confidence_score=1.0,  # Google doesn't provide confidence
                detected_language=detected_language,
                translation_engine=self.service_name,
                processing_time=processing_time,
                metadata={'api_method': 'rest_api'}
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return TranslationResult(
                original_text=request.text,
                translated_text="",
                source_language=request.source_language,
                target_language=request.target_language,
                translation_engine=self.service_name,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def translate_batch(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Translate a batch of texts."""
        if not requests:
            return []
        
        try:
            if not self.client:
                raise Exception("Google Translate client not initialized")
            
            # Group requests by language pair
            grouped_requests = self._group_requests_by_language(requests)
            
            results = []
            
            for (source_lang, target_lang), group_requests in grouped_requests.items():
                # Prepare texts for batch translation
                texts = [req.text for req in group_requests]
                source_language = None if source_lang.lower() == 'auto' else source_lang
                
                try:
                    # Use true batch translation with Google Translate REST API
                    batch_texts = [req.text for req in group_requests]
                    batch_results = self._translate_batch_with_rest_api(
                        batch_texts,
                        source_lang,
                        target_lang
                    )

                    # Create results from batch response
                    for i, original_request in enumerate(group_requests):
                        if i < len(batch_results):
                            translated_text, detected_language = batch_results[i]
                        else:
                            translated_text, detected_language = original_request.text, source_lang

                        result = TranslationResult(
                            original_text=original_request.text,
                            translated_text=translated_text,
                            source_language=detected_language,
                            target_language=target_lang,
                            confidence_score=1.0,
                            detected_language=detected_language,
                            translation_engine=self.service_name,
                            processing_time=0.1,  # Estimated
                            metadata={'api_method': 'rest_api_batch'}
                        )
                        results.append(result)
                
                except Exception as e:
                    # Handle batch failure
                    for request in group_requests:
                        result = TranslationResult(
                            original_text=request.text,
                            translated_text="",
                            source_language=request.source_language,
                            target_language=request.target_language,
                            translation_engine=self.service_name,
                            error_message=str(e)
                        )
                        results.append(result)
            
            return results
            
        except Exception as e:
            # Handle complete failure
            results = []
            for request in requests:
                result = TranslationResult(
                    original_text=request.text,
                    translated_text="",
                    source_language=request.source_language,
                    target_language=request.target_language,
                    translation_engine=self.service_name,
                    error_message=str(e)
                )
                results.append(result)
            return results
    
    async def detect_language(self, text: str) -> Optional[str]:
        """Detect the language of the text."""
        try:
            if not self.client:
                return None
            
            result = self.client.detect_language(text)
            return result['language']
            
        except Exception:
            return None
    
    async def get_supported_languages(self) -> Dict[str, str]:
        """Get supported languages."""
        try:
            if not self.client:
                return {}
            
            languages = self.client.get_languages()
            return {lang['language']: lang['name'] for lang in languages}
            
        except Exception:
            return {}
    
    async def validate_api_key(self) -> bool:
        """Validate the API key."""
        try:
            if not self.client:
                return False
            
            # Try a simple translation to test the API key
            test_result = self.client.translate('test', target_language='es')
            return test_result is not None
            
        except Exception:
            return False
    
    def get_service_name(self) -> str:
        """Get the service name."""
        return self.service_name
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information."""
        return {
            'requests_per_minute': 1000,  # Default limit
            'characters_per_request': 30000,
            'characters_per_minute': 2000000,
            'requests_per_day': 100000000
        }
    
    async def check_service_health(self) -> bool:
        """Check if the service is available."""
        try:
            if not self.client:
                return False
            
            # Simple health check with minimal API call
            languages = self.client.get_languages()
            return len(languages) > 0
            
        except Exception:
            return False
    
    def _group_requests_by_language(self, requests: List[TranslationRequest]) -> Dict[tuple, List[TranslationRequest]]:
        """Group requests by source and target language."""
        grouped = {}
        
        for request in requests:
            key = (request.source_language, request.target_language)
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(request)
        
        return grouped
    
    def _should_preserve_text(self, text: str) -> bool:
        """Check if text should be preserved (not translated)."""
        import re
        
        # Preserve text in brackets
        bracket_patterns = [
            r'\[.*?\]',     # Square brackets
            r'「.*?」',      # Japanese quotation marks
            r'【.*?】',      # Japanese brackets
        ]
        
        for pattern in bracket_patterns:
            if re.fullmatch(pattern, text.strip()):
                return True
        
        return False
