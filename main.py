#!/usr/local/bin/python3
"""
Excel Translator Application - Main Entry Point
"""

import sys
import os
import warnings
from pathlib import Path

# Suppress macOS system warnings for PyQt6 applications
if sys.platform == "darwin":  # macOS only
    # Suppress Qt logging messages (including TSM warnings)
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false;qt.qpa.input*=false'

    # Suppress Python warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    # Also suppress specific Qt-related warnings
    warnings.filterwarnings("ignore", message=".*TSM.*")
    warnings.filterwarnings("ignore", message=".*UIServer.*")

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from gui.windows.main_window import MainWindow
from gui.managers.localization_manager import get_localization_manager
from config.settings import ApplicationSettings
from helpers.logger import configure_logging, get_logger
from infrastructure.file_handlers.excel_handler import ExcelHandler
from infrastructure.plugins.google_translator import GoogleTranslator
from infrastructure.plugins.deepl_translator import DeepLTranslator


class TranslationApp:
    """Main application class."""
    
    def __init__(self):
        """Initialize the application."""
        # Load settings with proper priority: env vars override file settings for API keys
        settings_file = "config/app_settings.json"
        if os.path.exists(settings_file):
            self.settings = ApplicationSettings.load_from_file(settings_file)
            # Override API keys with environment variables if they exist
            env_google_key = os.getenv('GOOGLE_TRANSLATE_API_KEY', '').strip()
            env_deepl_key = os.getenv('DEEPL_API_KEY', '').strip()
            if env_google_key:
                self.settings.api.google_api_key = env_google_key
            if env_deepl_key:
                self.settings.api.deepl_api_key = env_deepl_key
        else:
            self.settings = ApplicationSettings.load_from_env()

        self.logger = None
        self.app = None
        self.main_window = None

        self.excel_handler = None
        self.google_translator = None
        self.deepl_translator = None
    
    def setup_logging(self):
        """Setup application logging."""
        configure_logging(
            log_level=self.settings.logging.log_level,
            log_to_file=self.settings.logging.log_to_file,
            log_file_path=self.settings.logging.log_file_path
        )
        self.logger = get_logger()
        self.logger.info("Application logging configured")
    
    def setup_directories(self):
        """Setup required directories."""
        self.settings.create_directories()
        self.logger.info("Required directories created")

    def setup_localization(self):
        """Setup localization manager with saved language."""
        localization_manager = get_localization_manager()

        # Set language from settings
        if self.settings and self.settings.ui.language:
            localization_manager.set_language(self.settings.ui.language)
            self.logger.info(f"Language set to: {self.settings.ui.language}")
        else:
            self.logger.info("Using default language: en")
    
    def setup_services(self):
        """Setup business logic services."""
        self.excel_handler = ExcelHandler()

        # Setup Google Translator with validation
        if self.settings.api.google_api_key:
            try:
                self.google_translator = GoogleTranslator(self.settings.api.google_api_key)
                if self.logger:
                    self.logger.info("Google Translator initialized successfully")
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Failed to initialize Google Translator: {e}")
                    self.logger.warning("Google API key may be invalid or expired")
                self.google_translator = None

        # Setup DeepL Translator with validation
        if self.settings.api.deepl_api_key:
            try:
                self.deepl_translator = DeepLTranslator(self.settings.api.deepl_api_key)
                if self.logger:
                    self.logger.info("DeepL Translator initialized successfully")
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Failed to initialize DeepL Translator: {e}")
                    self.logger.warning("DeepL API key may be invalid or expired")
                self.deepl_translator = None

        # Check translation services availability and set default
        google_available = (self.google_translator and
                          hasattr(self.google_translator, 'client') and
                          self.google_translator.client is not None)
        deepl_available = (self.deepl_translator and
                         hasattr(self.deepl_translator, 'client') and
                         self.deepl_translator.client is not None)

        # Log available services
        if self.logger:
            if google_available and deepl_available:
                self.logger.info("Both Google Translate and DeepL services initialized successfully")
                self.logger.info(f"Default translation engine: {self.settings.api.default_translation_engine}")
            elif deepl_available:
                self.logger.info("DeepL Translator service initialized successfully")
                self.logger.info("DeepL set as primary translation service")
            elif google_available:
                self.logger.info("Google Translate service initialized successfully")
                self.logger.info("Google Translate set as primary translation service")
            else:
                if self.settings.api.google_api_key or self.settings.api.deepl_api_key:
                    self.logger.warning("Translation services configured but failed to initialize properly")
                else:
                    self.logger.info("No translation API keys configured - translation features will be disabled")

        # Ensure default translator is set correctly based on availability
        if deepl_available and self.settings.api.default_translation_engine != "deepl":
            self.settings.api.default_translation_engine = "deepl"
            if self.logger:
                self.logger.info("Default translation engine set to DeepL")
        elif google_available and not deepl_available and self.settings.api.default_translation_engine != "google":
            self.settings.api.default_translation_engine = "google"
            if self.logger:
                self.logger.info("Default translation engine set to Google Translate (DeepL not available)")
    
    def setup_qt_application(self):
        """Setup the Qt application."""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("Excel Translator")
        self.app.setApplicationVersion("2.0.0")
        self.app.setOrganizationName("Translation Tools")

        font = QFont(self.settings.ui.font_family, self.settings.ui.font_size)
        self.app.setFont(font)

        if self.logger:
            self.logger.info("Qt application configured")
        else:
            print("Qt application configured")

        return self.app
    
    def setup_main_window(self):
        """Setup the main window."""
        self.main_window = MainWindow(
            settings=self.settings,
            excel_handler=self.excel_handler,
            google_translator=self.google_translator,
            deepl_translator=self.deepl_translator,
            logger=self.logger
        )
        
        self.main_window.resize(
            self.settings.ui.window_width,
            self.settings.ui.window_height
        )
        
        self.logger.info("Main window initialized")
    
    def center_window(self):
        """Center the main window on screen."""
        if self.main_window and self.app:
            screen = self.app.primaryScreen().geometry()
            window_geometry = self.main_window.frameGeometry()
            center_point = screen.center()
            window_geometry.moveCenter(center_point)
            self.main_window.move(window_geometry.topLeft())
    
    def validate_configuration(self):
        """Validate application configuration."""
        validation_result = self.settings.validate()

        if validation_result['errors']:
            error_msg = "Configuration errors found:\n" + "\n".join(validation_result['errors'])
            if self.logger:
                self.logger.error(f"Configuration validation failed: {error_msg}")
            else:
                print(f"Configuration validation failed: {error_msg}")

            if self.app:
                QMessageBox.critical(None, "Configuration Error", error_msg)
            return False

        if validation_result['warnings']:
            warning_msg = "Configuration warnings:\n" + "\n".join(validation_result['warnings'])
            if self.logger:
                self.logger.warning(f"Configuration warnings: {warning_msg}")
            else:
                print(f"Configuration warnings: {warning_msg}")

            # Show helpful message for API key issues
            if any('API key' in warning for warning in validation_result['warnings']):
                self._show_api_key_setup_info(validation_result['warnings'])

        return True

    def _show_api_key_setup_info(self, warnings=None):
        """Show helpful information about setting up API keys."""
        if warnings is None:
            warnings = []

        # Determine the type of API key issue
        has_missing_keys = any('No translation API keys configured' in warning for warning in warnings)
        has_invalid_keys = any('API key issue' in warning or 'API key warning' in warning for warning in warnings)
        has_no_valid_keys = any('No valid translation API keys found' in warning for warning in warnings)

        if self.app:
            from PyQt6.QtWidgets import QMessageBox
            from PyQt6.QtCore import Qt

            msg_box = QMessageBox()

            if has_missing_keys:
                # No API keys configured
                msg_box.setIcon(QMessageBox.Icon.Information)
                msg_box.setWindowTitle("API Keys Setup")
                msg_box.setText("Welcome to Excel Translator!")
                msg_box.setInformativeText(
                    "To enable translation functionality, you'll need to configure API keys for translation services.\n\n"
                    "You can set up API keys by:\n"
                    "1. Going to Preferences → Settings (Cmd+, or Ctrl+,)\n"
                    "2. Clicking the 'API Keys' tab\n"
                    "3. Adding your Google Translate or DeepL API key\n\n"
                    "The application will work without API keys, but translation features will be disabled until you add them."
                )
            elif has_invalid_keys or has_no_valid_keys:
                # Invalid or problematic API keys
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("API Keys Issue")
                msg_box.setText("API Key Configuration Problem")

                # Extract specific error messages
                api_issues = []
                for warning in warnings:
                    if 'API key issue' in warning or 'API key warning' in warning:
                        api_issues.append(warning)

                issue_text = "\n".join(api_issues) if api_issues else "There are issues with your configured API keys."

                msg_box.setInformativeText(
                    f"{issue_text}\n\n"
                    "Please check your API keys:\n"
                    "1. Go to Preferences → Settings (Cmd+, or Ctrl+,)\n"
                    "2. Click the 'API Keys' tab\n"
                    "3. Verify your API keys are correct and valid\n\n"
                    "Common issues:\n"
                    "• Google API keys should be 35+ characters\n"
                    "• DeepL API keys must end with :fx (free) or :px (pro)\n"
                    "• Keys should not contain spaces or special characters\n"
                    "• Keys may be expired or have insufficient permissions"
                )
            else:
                # Generic API key info
                msg_box.setIcon(QMessageBox.Icon.Information)
                msg_box.setWindowTitle("API Keys Info")
                msg_box.setText("Translation Service Configuration")
                msg_box.setInformativeText(
                    "Please check your translation service configuration in Preferences → Settings → API Keys."
                )

            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)

            # Don't block startup - just show the info
            msg_box.show()
            msg_box.raise_()
            msg_box.activateWindow()
        else:
            # If no app yet, just print the info
            if has_missing_keys:
                print("API Keys Setup Info:")
                print("To enable translation functionality, configure API keys in Preferences → Settings → API Keys")
                print("The application will work without API keys, but translation features will be disabled.")
            elif has_invalid_keys or has_no_valid_keys:
                print("API Keys Issue:")
                for warning in warnings:
                    if 'API key' in warning:
                        print(f"  - {warning}")
                print("Please check your API keys in Preferences → Settings → API Keys")
            else:
                print("API Keys Info:")
                print("Please check your translation service configuration.")
    
    def run(self):
        """Run the application."""
        try:
            # Run synchronously without complex async setup
            return self.run_sync()
        except KeyboardInterrupt:
            if self.logger:
                self.logger.info("Application interrupted by user")
            else:
                print("Application interrupted by user")
            return 0
    
    def run_sync(self):
        """Synchronous application entry point."""
        try:
            # Setup components
            self.setup_logging()
            self.setup_directories()
            self.setup_localization()

            # Validate configuration
            if not self.validate_configuration():
                return 1

            # Setup services and UI
            self.setup_services()
            self.setup_qt_application()
            self.setup_main_window()
            
            # Show window
            if self.main_window:
                self.main_window.show()
                self.center_window()
            
                if self.logger:
                    self.logger.info("Excel Translator application started successfully")
                else:
                    print("Excel Translator application started successfully!")
            
            # Run the Qt event loop synchronously
            return self.app.exec() if self.app else 1
            
        except Exception as e:
            error_msg = f"Error starting application: {e}"
            if self.logger:
                self.logger.critical(error_msg)
            else:
                print(error_msg)
            
            if self.app:
                QMessageBox.critical(None, "Application Error", error_msg)
            
            return 1


def main():
    """Main application entry point."""
    app = TranslationApp()
    exit_code = app.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
