"""
Formatting domain entity for preserving Excel cell formatting.
"""

from typing import Optional, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum


class BorderStyle(Enum):
    """Border style enumeration."""
    NONE = "none"
    THIN = "thin"
    MEDIUM = "medium"
    THICK = "thick"
    DOUBLE = "double"
    DASHED = "dashed"
    DOTTED = "dotted"


class AlignmentType(Enum):
    """Alignment type enumeration."""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"
    JUSTIFY = "justify"
    TOP = "top"
    MIDDLE = "middle"
    BOTTOM = "bottom"


@dataclass
class FontProperties:
    """Font properties for cell formatting."""
    
    name: Optional[str] = None
    size: Optional[float] = None
    bold: bool = False
    italic: bool = False
    underline: bool = False
    strikethrough: bool = False
    color: Optional[str] = None
    theme_color: Optional[str] = None


@dataclass
class BorderProperties:
    """Border properties for cell formatting."""
    
    left_style: BorderStyle = BorderStyle.NONE
    right_style: BorderStyle = BorderStyle.NONE
    top_style: BorderStyle = BorderStyle.NONE
    bottom_style: BorderStyle = BorderStyle.NONE
    left_color: Optional[str] = None
    right_color: Optional[str] = None
    top_color: Optional[str] = None
    bottom_color: Optional[str] = None


@dataclass
class FillProperties:
    """Fill properties for cell formatting."""
    
    pattern_type: Optional[str] = None
    foreground_color: Optional[str] = None
    background_color: Optional[str] = None
    theme_color: Optional[str] = None


@dataclass
class AlignmentProperties:
    """Alignment properties for cell formatting."""
    
    horizontal: Optional[AlignmentType] = None
    vertical: Optional[AlignmentType] = None
    wrap_text: bool = False
    shrink_to_fit: bool = False
    indent: int = 0
    text_rotation: int = 0


@dataclass
class NumberFormat:
    """Number format properties."""
    
    format_code: Optional[str] = None
    format_type: Optional[str] = None
    is_date: bool = False
    is_time: bool = False
    is_currency: bool = False
    is_percentage: bool = False


@dataclass
class CellDimensions:
    """Cell dimensions and position."""

    row: int
    column: int
    row_height: Optional[float] = None
    column_width: Optional[float] = None
    merged_range: Optional[str] = None
    is_merged: bool = False
    is_merged_top_left: bool = False
    merged_bounds: Optional[tuple] = None


@dataclass
class Formatting:
    """Domain entity representing complete cell formatting."""
    
    cell_dimensions: CellDimensions
    font: FontProperties = field(default_factory=FontProperties)
    border: BorderProperties = field(default_factory=BorderProperties)
    fill: FillProperties = field(default_factory=FillProperties)
    alignment: AlignmentProperties = field(default_factory=AlignmentProperties)
    number_format: NumberFormat = field(default_factory=NumberFormat)
    protection_locked: bool = True
    protection_hidden: bool = False
    comment: Optional[str] = None
    hyperlink: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def cell_address(self) -> str:
        """Get cell address in Excel format (e.g., A1, B2)."""
        column_letter = self._number_to_column_letter(self.cell_dimensions.column)
        return f"{column_letter}{self.cell_dimensions.row}"
    
    @property
    def has_formatting(self) -> bool:
        """Check if cell has any formatting applied."""
        return (
            self.font.name is not None or
            self.font.size is not None or
            self.font.bold or
            self.font.italic or
            self.font.color is not None or
            self.border.left_style != BorderStyle.NONE or
            self.border.right_style != BorderStyle.NONE or
            self.border.top_style != BorderStyle.NONE or
            self.border.bottom_style != BorderStyle.NONE or
            self.fill.foreground_color is not None or
            self.fill.background_color is not None or
            self.alignment.horizontal is not None or
            self.alignment.vertical is not None or
            self.number_format.format_code is not None
        )
    
    @property
    def has_borders(self) -> bool:
        """Check if cell has any borders."""
        return (
            self.border.left_style != BorderStyle.NONE or
            self.border.right_style != BorderStyle.NONE or
            self.border.top_style != BorderStyle.NONE or
            self.border.bottom_style != BorderStyle.NONE
        )
    
    @property
    def has_fill(self) -> bool:
        """Check if cell has fill formatting."""
        return (
            self.fill.foreground_color is not None or
            self.fill.background_color is not None
        )
    
    def _number_to_column_letter(self, column_number: int) -> str:
        """Convert column number to Excel column letter."""
        result = ""
        while column_number > 0:
            column_number -= 1
            result = chr(65 + (column_number % 26)) + result
            column_number //= 26
        return result
    
    def copy(self) -> 'Formatting':
        """Create a copy of the formatting."""
        import copy
        return copy.deepcopy(self)
    
    def apply_to_cell(self, cell, preserve_content: bool = True) -> None:
        """Apply formatting to an openpyxl cell object."""
        try:
            from openpyxl.styles import Font, Border, Side, PatternFill, Alignment, Protection
            
            # Apply font formatting
            if self.font.name or self.font.size or self.font.bold or self.font.italic or self.font.color:
                font_color = self._normalize_color(self.font.color) if self.font.color else None
                cell.font = Font(
                    name=self.font.name,
                    size=self.font.size,
                    bold=self.font.bold,
                    italic=self.font.italic,
                    underline='single' if self.font.underline else 'none',
                    strike=self.font.strikethrough,
                    color=font_color
                )
            
            # Apply border formatting
            if self.has_borders:
                left_color = self._normalize_color(self.border.left_color) if self.border.left_color else None
                right_color = self._normalize_color(self.border.right_color) if self.border.right_color else None
                top_color = self._normalize_color(self.border.top_color) if self.border.top_color else None
                bottom_color = self._normalize_color(self.border.bottom_color) if self.border.bottom_color else None

                left_side = Side(style=self.border.left_style.value, color=left_color)
                right_side = Side(style=self.border.right_style.value, color=right_color)
                top_side = Side(style=self.border.top_style.value, color=top_color)
                bottom_side = Side(style=self.border.bottom_style.value, color=bottom_color)

                cell.border = Border(
                    left=left_side,
                    right=right_side,
                    top=top_side,
                    bottom=bottom_side
                )
            
            # Apply fill formatting
            if self.has_fill:
                fg_color = self._normalize_color(self.fill.foreground_color) if self.fill.foreground_color else None
                bg_color = self._normalize_color(self.fill.background_color) if self.fill.background_color else None

                cell.fill = PatternFill(
                    start_color=fg_color,
                    end_color=bg_color,
                    fill_type=self.fill.pattern_type or 'solid'
                )
            
            # Apply alignment
            if self.alignment.horizontal or self.alignment.vertical:
                cell.alignment = Alignment(
                    horizontal=self.alignment.horizontal.value if self.alignment.horizontal else None,
                    vertical=self.alignment.vertical.value if self.alignment.vertical else None,
                    wrap_text=self.alignment.wrap_text,
                    shrink_to_fit=self.alignment.shrink_to_fit,
                    indent=self.alignment.indent,
                    text_rotation=self.alignment.text_rotation
                )
            
            # Apply number format
            if self.number_format.format_code:
                cell.number_format = self.number_format.format_code
            
            # Apply protection
            cell.protection = Protection(
                locked=self.protection_locked,
                hidden=self.protection_hidden
            )
            
        except ImportError:
            # openpyxl not available, skip formatting
            pass
        except Exception as e:
            # Log error but don't fail the operation
            print(f"Warning: Could not apply formatting to cell: {e}")

    def _normalize_color(self, color_value):
        """Normalize color value to aRGB hex format required by openpyxl."""
        if not color_value:
            return None

        try:
            # Remove any prefix and ensure it's a string
            color_str = str(color_value).strip()

            # Remove common prefixes
            if color_str.startswith('0x'):
                color_str = color_str[2:]
            elif color_str.startswith('#'):
                color_str = color_str[1:]

            # Ensure it's uppercase
            color_str = color_str.upper()

            # Handle different color formats
            if len(color_str) == 6:
                # RGB format - add alpha channel (FF for fully opaque)
                return f"FF{color_str}"
            elif len(color_str) == 8:
                # Already aRGB format
                return color_str
            elif len(color_str) == 3:
                # Short RGB format (e.g., "F0A") - expand to full format
                expanded = ''.join([c*2 for c in color_str])
                return f"FF{expanded}"
            else:
                # Invalid format, return None to use default
                return None

        except Exception:
            # If any error occurs, return None to use default
            return None

    def extract_from_cell(self, cell) -> None:
        """Extract formatting from an openpyxl cell object."""
        try:
            # Extract font properties
            if hasattr(cell, 'font') and cell.font:
                self.font.name = cell.font.name
                self.font.size = cell.font.size
                self.font.bold = cell.font.bold
                self.font.italic = cell.font.italic
                self.font.underline = cell.font.underline != 'none'
                self.font.strikethrough = cell.font.strike
                if hasattr(cell.font, 'color') and cell.font.color:
                    self.font.color = str(cell.font.color.rgb) if hasattr(cell.font.color, 'rgb') else None
            
            # Extract border properties
            if hasattr(cell, 'border') and cell.border:
                if cell.border.left and cell.border.left.style:
                    self.border.left_style = BorderStyle(cell.border.left.style)
                    if cell.border.left.color:
                        self.border.left_color = str(cell.border.left.color.rgb) if hasattr(cell.border.left.color, 'rgb') else None
                
                if cell.border.right and cell.border.right.style:
                    self.border.right_style = BorderStyle(cell.border.right.style)
                    if cell.border.right.color:
                        self.border.right_color = str(cell.border.right.color.rgb) if hasattr(cell.border.right.color, 'rgb') else None
                
                if cell.border.top and cell.border.top.style:
                    self.border.top_style = BorderStyle(cell.border.top.style)
                    if cell.border.top.color:
                        self.border.top_color = str(cell.border.top.color.rgb) if hasattr(cell.border.top.color, 'rgb') else None
                
                if cell.border.bottom and cell.border.bottom.style:
                    self.border.bottom_style = BorderStyle(cell.border.bottom.style)
                    if cell.border.bottom.color:
                        self.border.bottom_color = str(cell.border.bottom.color.rgb) if hasattr(cell.border.bottom.color, 'rgb') else None
            
            # Extract fill properties
            if hasattr(cell, 'fill') and cell.fill:
                self.fill.pattern_type = cell.fill.fill_type
                if hasattr(cell.fill, 'start_color') and cell.fill.start_color:
                    self.fill.foreground_color = str(cell.fill.start_color.rgb) if hasattr(cell.fill.start_color, 'rgb') else None
                if hasattr(cell.fill, 'end_color') and cell.fill.end_color:
                    self.fill.background_color = str(cell.fill.end_color.rgb) if hasattr(cell.fill.end_color, 'rgb') else None
            
            # Extract alignment properties
            if hasattr(cell, 'alignment') and cell.alignment:
                if cell.alignment.horizontal:
                    try:
                        self.alignment.horizontal = AlignmentType(cell.alignment.horizontal)
                    except ValueError:
                        pass  # Unknown alignment type
                
                if cell.alignment.vertical:
                    try:
                        self.alignment.vertical = AlignmentType(cell.alignment.vertical)
                    except ValueError:
                        pass  # Unknown alignment type
                
                self.alignment.wrap_text = cell.alignment.wrap_text or False
                self.alignment.shrink_to_fit = cell.alignment.shrink_to_fit or False
                self.alignment.indent = cell.alignment.indent or 0
                self.alignment.text_rotation = cell.alignment.text_rotation or 0
            
            # Extract number format
            if hasattr(cell, 'number_format') and cell.number_format:
                self.number_format.format_code = cell.number_format
                # Determine format type based on format code
                format_code_lower = cell.number_format.lower()
                if 'date' in format_code_lower or 'yyyy' in format_code_lower or 'mm' in format_code_lower:
                    self.number_format.is_date = True
                elif 'time' in format_code_lower or 'hh' in format_code_lower:
                    self.number_format.is_time = True
                elif '$' in cell.number_format or '€' in cell.number_format or '¥' in cell.number_format:
                    self.number_format.is_currency = True
                elif '%' in cell.number_format:
                    self.number_format.is_percentage = True
            
            # Extract protection
            if hasattr(cell, 'protection') and cell.protection:
                self.protection_locked = cell.protection.locked
                self.protection_hidden = cell.protection.hidden
            
            # Extract comment
            if hasattr(cell, 'comment') and cell.comment:
                self.comment = cell.comment.text
            
            # Extract hyperlink
            if hasattr(cell, 'hyperlink') and cell.hyperlink:
                self.hyperlink = cell.hyperlink.target
                
        except Exception as e:
            # Log error but don't fail the operation
            print(f"Warning: Could not extract formatting from cell: {e}")
