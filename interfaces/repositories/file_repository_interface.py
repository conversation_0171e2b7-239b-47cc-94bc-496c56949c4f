"""
File repository interface for file operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from pathlib import Path

from domain.entities.excel_file import ExcelFile


class FileRepositoryInterface(ABC):
    """Interface for file repository operations."""
    
    @abstractmethod
    async def load_file(self, file_path: Path) -> Optional[ExcelFile]:
        """Load an Excel file and return ExcelFile entity."""
        pass
    
    @abstractmethod
    async def save_file(self, excel_file: ExcelFile, output_path: Path) -> bool:
        """Save an Excel file to the specified path."""
        pass
    
    @abstractmethod
    async def validate_file(self, file_path: Path) -> bool:
        """Validate if the file is a valid Excel file."""
        pass
    
    @abstractmethod
    async def get_file_info(self, file_path: Path) -> Optional[dict]:
        """Get basic file information."""
        pass
    
    @abstractmethod
    async def backup_file(self, file_path: Path, backup_dir: Path) -> bool:
        """Create a backup of the file."""
        pass
    
    @abstractmethod
    async def list_files(self, directory: Path, extensions: List[str]) -> List[Path]:
        """List files in directory with specified extensions."""
        pass
