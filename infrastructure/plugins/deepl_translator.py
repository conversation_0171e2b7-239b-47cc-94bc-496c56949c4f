"""
DeepL API implementation.
"""

from typing import List, Optional, Dict, Any
import time
import os

try:
    import deepl
    DEEPL_AVAILABLE = True
except ImportError:
    DEEPL_AVAILABLE = False
    deepl = None

from interfaces.services.translation_service_interface import TranslationServiceInterface
from domain.entities.translation import TranslationRequest, TranslationResult


class DeepLTranslator(TranslationServiceInterface):
    """DeepL API implementation."""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize DeepL Translator."""
        self.api_key = api_key or os.getenv('DEEPL_API_KEY')
        self.service_name = "DeepL"
        self.client = None
        self._initialize_client()

    def is_available(self) -> bool:
        """Check if the translator is available."""
        return DEEPL_AVAILABLE and self.client is not None

    def _convert_to_deepl_language_code(self, language_code: str, is_target: bool = True) -> str:
        """Convert internal language code to DeepL API format.

        Args:
            language_code: The language code to convert
            is_target: True if this is a target language, False if source language
        """
        if not language_code or language_code.lower() == 'auto':
            return None

        code_lower = language_code.lower()

        # DeepL has different requirements for source vs target languages
        if is_target:
            # Target language mappings (more specific variants required)
            target_mappings = {
                'en': 'EN-US',  # Use EN-US as default English variant for target
                'pt': 'PT-PT',  # Use PT-PT as default Portuguese variant for target
                'zh': 'ZH',     # Chinese (simplified)
            }
            if code_lower in target_mappings:
                return target_mappings[code_lower]
        else:
            # Source language mappings (standard codes often work)
            source_mappings = {
                'en': 'EN',     # EN works for source language
                'pt': 'PT',     # PT works for source language
                'zh': 'ZH',     # Chinese (simplified)
            }
            if code_lower in source_mappings:
                return source_mappings[code_lower]

        # For other languages, use uppercase
        return language_code.upper()
    
    def _initialize_client(self):
        """Initialize the DeepL client."""
        try:
            if self.api_key and not self.api_key.startswith('your_'):
                self.client = deepl.Translator(self.api_key)
            else:
                self.client = None
        except Exception:
            self.client = None
    
    async def translate_text(self, request: TranslationRequest) -> TranslationResult:
        """Translate a single text."""
        start_time = time.time()

        try:
            if not self.client:
                raise Exception("DeepL client not initialized")

            # Prepare translation parameters using proper DeepL language codes
            source_lang = self._convert_to_deepl_language_code(request.source_language, is_target=False)
            target_lang = self._convert_to_deepl_language_code(request.target_language, is_target=True)

            # Call DeepL API
            result = self.client.translate_text(
                request.text,
                target_lang=target_lang,
                source_lang=source_lang
            )
            
            processing_time = time.time() - start_time
            
            # Extract results
            translated_text = result.text
            detected_language = result.detected_source_lang.lower() if result.detected_source_lang else request.source_language
            
            return TranslationResult(
                original_text=request.text,
                translated_text=translated_text,
                source_language=detected_language,
                target_language=request.target_language,
                confidence_score=1.0,  # DeepL doesn't provide confidence scores
                detected_language=detected_language,
                translation_engine=self.service_name,
                processing_time=processing_time,
                metadata={'detected_source_lang': result.detected_source_lang}
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return TranslationResult(
                original_text=request.text,
                translated_text="",
                source_language=request.source_language,
                target_language=request.target_language,
                translation_engine=self.service_name,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def translate_batch(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Translate a batch of texts."""
        if not requests:
            return []
        
        try:
            if not self.client:
                raise Exception("DeepL client not initialized")
            
            # Group requests by language pair
            grouped_requests = self._group_requests_by_language(requests)
            
            results = []
            
            for (source_lang, target_lang), group_requests in grouped_requests.items():
                # Prepare texts for batch translation
                texts = [req.text for req in group_requests]
                source_language = self._convert_to_deepl_language_code(source_lang, is_target=False)
                target_language = self._convert_to_deepl_language_code(target_lang, is_target=True)

                try:
                    # Call DeepL API for batch
                    batch_results = self.client.translate_text(
                        texts,
                        target_lang=target_language,
                        source_lang=source_language
                    )
                    
                    # Ensure batch_results is a list
                    if not isinstance(batch_results, list):
                        batch_results = [batch_results]
                    
                    # Process results
                    for original_request, api_result in zip(group_requests, batch_results):
                        translated_text = api_result.text
                        detected_language = api_result.detected_source_lang.lower() if api_result.detected_source_lang else source_lang
                        
                        result = TranslationResult(
                            original_text=original_request.text,
                            translated_text=translated_text,
                            source_language=detected_language,
                            target_language=target_lang,
                            confidence_score=1.0,
                            detected_language=detected_language,
                            translation_engine=self.service_name,
                            processing_time=0.1,  # Estimated
                            metadata={'detected_source_lang': api_result.detected_source_lang}
                        )
                        results.append(result)
                
                except Exception as e:
                    # Handle batch failure
                    for request in group_requests:
                        result = TranslationResult(
                            original_text=request.text,
                            translated_text="",
                            source_language=request.source_language,
                            target_language=request.target_language,
                            translation_engine=self.service_name,
                            error_message=str(e)
                        )
                        results.append(result)
            
            return results
            
        except Exception as e:
            # Handle complete failure
            results = []
            for request in requests:
                result = TranslationResult(
                    original_text=request.text,
                    translated_text="",
                    source_language=request.source_language,
                    target_language=request.target_language,
                    translation_engine=self.service_name,
                    error_message=str(e)
                )
                results.append(result)
            return results
    
    async def detect_language(self, text: str) -> Optional[str]:
        """Detect the language of the text."""
        try:
            if not self.client:
                return None

            # DeepL doesn't have a separate detect API,
            # we can use translate with auto-detect and check detected language
            # Use EN-US instead of deprecated EN
            result = self.client.translate_text(text, target_lang="EN-US")
            return result.detected_source_lang.lower() if result.detected_source_lang else None

        except Exception:
            return None
    
    async def get_supported_languages(self) -> Dict[str, str]:
        """Get supported languages."""
        try:
            if not self.client:
                return {}
            
            source_languages = self.client.get_source_languages()
            target_languages = self.client.get_target_languages()
            
            # Combine source and target languages
            languages = {}
            for lang in source_languages:
                languages[lang.code.lower()] = lang.name
            
            for lang in target_languages:
                languages[lang.code.lower()] = lang.name
            
            return languages
            
        except Exception:
            return {}
    
    async def validate_api_key(self) -> bool:
        """Validate the API key."""
        try:
            if not self.client:
                return False
            
            # Try to get usage information to test the API key
            usage = self.client.get_usage()
            return usage is not None
            
        except Exception:
            return False
    
    def get_service_name(self) -> str:
        """Get the service name."""
        return self.service_name
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information."""
        return {
            'requests_per_minute': 100,  # Conservative estimate
            'characters_per_request': 10000,
            'characters_per_month': 500000,  # Free tier limit
            'requests_per_day': 10000
        }
    
    async def check_service_health(self) -> bool:
        """Check if the service is available."""
        try:
            if not self.client:
                return False
            
            # Simple health check by getting supported languages
            languages = self.client.get_source_languages()
            return len(languages) > 0
            
        except Exception:
            return False
    
    def _group_requests_by_language(self, requests: List[TranslationRequest]) -> Dict[tuple, List[TranslationRequest]]:
        """Group requests by source and target language."""
        grouped = {}
        
        for request in requests:
            key = (request.source_language, request.target_language)
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(request)
        
        return grouped
    
    def _should_preserve_text(self, text: str) -> bool:
        """Check if text should be preserved (not translated)."""
        import re
        
        # Preserve text in brackets
        bracket_patterns = [
            r'\[.*?\]',     # Square brackets
            r'「.*?」',      # Japanese quotation marks
            r'【.*?】',      # Japanese brackets
        ]
        
        for pattern in bracket_patterns:
            if re.fullmatch(pattern, text.strip()):
                return True
        
        return False
