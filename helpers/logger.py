"""
Advanced logging utilities for the translation application.
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json


class TranslationLogger:
    """Advanced logger for translation operations."""
    
    def __init__(
        self,
        name: str = "translation_app",
        log_level: str = "INFO",
        log_to_file: bool = True,
        log_file_path: str = "logs/app.log",
        max_file_size_mb: float = 10.0,
        backup_count: int = 5
    ):
        """Initialize the logger."""
        self.name = name
        self.log_level = getattr(logging, log_level.upper())
        self.log_to_file = log_to_file
        self.log_file_path = Path(log_file_path)
        self.max_file_size = int(max_file_size_mb * 1024 * 1024)
        self.backup_count = backup_count
        
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.log_level)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        self._setup_formatters()
        self._setup_handlers()
    
    def _setup_formatters(self):
        """Setup log formatters."""
        self.console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        self.file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        self.json_formatter = JSONFormatter()
    
    def _setup_handlers(self):
        """Setup log handlers."""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(self.console_formatter)
        self.logger.addHandler(console_handler)

        if self.log_to_file:
            self.log_file_path.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.handlers.RotatingFileHandler(
                self.log_file_path,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(self.file_formatter)
            self.logger.addHandler(file_handler)
            
            json_log_path = self.log_file_path.with_suffix('.json')
            json_handler = logging.handlers.RotatingFileHandler(
                json_log_path,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            json_handler.setLevel(logging.INFO)
            json_handler.setFormatter(self.json_formatter)
            self.logger.addHandler(json_handler)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log info message."""
        self.logger.info(message, extra=extra or {})
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log debug message."""
        self.logger.debug(message, extra=extra or {})
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log warning message."""
        self.logger.warning(message, extra=extra or {})
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log error message."""
        self.logger.error(message, extra=extra or {})
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log critical message."""
        self.logger.critical(message, extra=extra or {})
    
    def log_translation_start(self, file_name: str, source_lang: str, target_lang: str, total_texts: int):
        """Log translation operation start."""
        self.info(f"Translation started", extra={
            'event_type': 'translation_start',
            'file_name': file_name,
            'source_language': source_lang,
            'target_language': target_lang,
            'total_texts': total_texts,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_translation_complete(self, file_name: str, duration: float, success_count: int, failed_count: int):
        """Log translation operation completion."""
        self.info(f"Translation completed", extra={
            'event_type': 'translation_complete',
            'file_name': file_name,
            'duration_seconds': duration,
            'successful_translations': success_count,
            'failed_translations': failed_count,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_translation_error(self, file_name: str, error_message: str, error_details: Optional[str] = None):
        """Log translation error."""
        self.error(f"Translation error", extra={
            'event_type': 'translation_error',
            'file_name': file_name,
            'error_message': error_message,
            'error_details': error_details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_file_operation(self, operation: str, file_path: str, success: bool, details: Optional[str] = None):
        """Log file operation."""
        level = self.info if success else self.error
        level(f"File {operation}", extra={
            'event_type': 'file_operation',
            'operation': operation,
            'file_path': file_path,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_api_call(self, service: str, method: str, duration: float, success: bool, details: Optional[str] = None):
        """Log API call."""
        level = self.info if success else self.warning
        level(f"API call {service}.{method}", extra={
            'event_type': 'api_call',
            'service': service,
            'method': method,
            'duration_seconds': duration,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_user_action(self, action: str, details: Optional[Dict[str, Any]] = None):
        """Log user action."""
        self.info(f"User action: {action}", extra={
            'event_type': 'user_action',
            'action': action,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        })
    
    def log_system_info(self, info: Dict[str, Any]):
        """Log system information."""
        self.info("System information", extra={
            'event_type': 'system_info',
            'system_info': info,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str = 'seconds'):
        """Log performance metric."""
        self.info(f"Performance metric: {metric_name}", extra={
            'event_type': 'performance_metric',
            'metric_name': metric_name,
            'value': value,
            'unit': unit,
            'timestamp': datetime.now().isoformat()
        })


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record):
        """Format log record as JSON."""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, '__dict__'):
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'exc_info',
                              'exc_text', 'stack_info', 'lineno', 'funcName',
                              'created', 'msecs', 'relativeCreated', 'thread',
                              'threadName', 'processName', 'process', 'getMessage']:
                    log_entry[key] = value
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)


class ContextLogger:
    """Context manager for logging operations with timing."""
    
    def __init__(self, logger: TranslationLogger, operation_name: str, **kwargs):
        """Initialize context logger."""
        self.logger = logger
        self.operation_name = operation_name
        self.kwargs = kwargs
        self.start_time = None
    
    def __enter__(self):
        """Enter context."""
        self.start_time = datetime.now()
        self.logger.info(f"Starting {self.operation_name}", extra={
            'event_type': 'operation_start',
            'operation': self.operation_name,
            **self.kwargs
        })
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context."""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.info(f"Completed {self.operation_name}", extra={
                'event_type': 'operation_complete',
                'operation': self.operation_name,
                'duration_seconds': duration,
                'success': True,
                **self.kwargs
            })
        else:
            self.logger.error(f"Failed {self.operation_name}", extra={
                'event_type': 'operation_failed',
                'operation': self.operation_name,
                'duration_seconds': duration,
                'success': False,
                'error_type': exc_type.__name__,
                'error_message': str(exc_val),
                **self.kwargs
            })


# Global logger instance
default_logger = TranslationLogger()


def get_logger(name: str = "translation_app") -> TranslationLogger:
    """Get a logger instance."""
    if name == "translation_app":
        return default_logger
    return TranslationLogger(name=name)


def configure_logging(
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_file_path: str = "logs/app.log"
):
    """Configure global logging settings."""
    global default_logger
    default_logger = TranslationLogger(
        log_level=log_level,
        log_to_file=log_to_file,
        log_file_path=log_file_path
    )
