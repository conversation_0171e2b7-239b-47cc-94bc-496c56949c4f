from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QLabel, QPushButton, QFrame, QFileDialog
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QFont
from ...styles.file_upload_style import FILE_UPLOAD_STYLE, FILE_UPLOAD_DRAG_STYLE
from ...managers.localization_manager import get_localization_manager, tr


class FileUpload(QFrame):
    file_selected = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self._current_file_size = None  # Store current file size for language updates
        self.setAcceptDrops(True)
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_texts)
    
    def setup_ui(self):
        self.setFixedHeight(285)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(40, 50, 40, 50)
        layout.setSpacing(25)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # File name label
        self.file_label = QLabel(tr("no_file_selected"))
        self.file_label.setObjectName("fileName")
        self.file_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.file_label)

        # Add spacing
        layout.addSpacing(10)

        # Select file button
        self.select_button = QPushButton(tr("browse_button"))
        self.select_button.setObjectName("selectFileButton")
        self.select_button.setFixedSize(140, 40)
        self.select_button.clicked.connect(self.open_file_dialog)  # Connect button signal
        layout.addWidget(self.select_button, 0, Qt.AlignmentFlag.AlignCenter)

        # Add spacing
        layout.addSpacing(10)

        # File size label
        self.size_label = QLabel("")
        self.size_label.setObjectName("fileSize")
        self.size_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.size_label)
    
    def apply_styles(self):
        self.setStyleSheet(FILE_UPLOAD_STYLE)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.setStyleSheet(FILE_UPLOAD_STYLE + FILE_UPLOAD_DRAG_STYLE)
    
    def dragLeaveEvent(self, event):
        self.apply_styles()
    
    def dropEvent(self, event: QDropEvent):
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        if files:
            file_path = files[0]
            self.file_selected.emit(file_path)
            # Update UI with dropped file info
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            self.file_label.setText(file_name)
            self._current_file_size = file_size  # Store for language updates
            size_text = self._format_file_size(file_size)
            self.size_label.setText(f"{tr('file_size')}: {size_text}")
        
        self.apply_styles()
        event.acceptProposedAction()

    def open_file_dialog(self):
        """Open file dialog to select Excel files"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr("select_excel_file"),
            "",
            tr("excel_files_filter")
        )
        
        if file_path:
            self.file_selected.emit(file_path)
            # Update UI with selected file info
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            self.file_label.setText(file_name)
            self._current_file_size = file_size  # Store for language updates
            size_text = self._format_file_size(file_size)
            self.size_label.setText(f"{tr('file_size')}: {size_text}")

    def _format_file_size(self, file_size):
        """Format file size with localized units."""
        if file_size < 1024:
            return f"{file_size} {tr('file_size_bytes')}"
        elif file_size < 1024 * 1024:
            return f"{file_size // 1024} {tr('file_size_kb')}"
        else:
            return f"{file_size // (1024 * 1024)} {tr('file_size_mb')}"

    def reset_state(self):
        """Reset the file upload component to initial state."""
        self.file_label.setText(tr("no_file_selected"))
        self.size_label.setText("")
        self._current_file_size = None  # Clear stored file size
        self.apply_styles()

    def update_texts(self):
        """Update all UI texts when language changes."""
        # Update button text
        self.select_button.setText(tr("browse_button"))

        # Update file label if no file is selected (check if it shows the default text)
        current_text = self.file_label.text()
        # Check if current text matches any of the "no file selected" translations
        no_file_translations = [
            "No file selected",  # English
            "ファイルが選択されていません",  # Japanese
            "Chưa chọn tệp"  # Vietnamese
        ]
        if current_text in no_file_translations:
            self.file_label.setText(tr("no_file_selected"))

        # Update file size label if it contains size information
        if self._current_file_size is not None:
            size_text = self._format_file_size(self._current_file_size)
            self.size_label.setText(f"{tr('file_size')}: {size_text}")
