from PyQt6.QtWidgets import Q<PERSON>omboBox
from PyQt6.QtCore import Qt
from ...styles.translator_combo_box_style import (
    TRANSLATOR_COMBO_BOX_STYLE,
    TRANSLATOR_COMBO_BOX_POPUP_STYLE,
    TRANSLATOR_COMBO_BOX_LIST_STYLE
)


class TranslatorComboBox(QComboBox):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        self.setFixedHeight(35)
        translators = [
            "DeepL Translator",
            "Google Translator"
        ]
        self.addItems(translators)
        self.setCurrentIndex(0)
    
    def apply_styles(self):
        self.setStyleSheet(TRANSLATOR_COMBO_BOX_STYLE)
        popup = self.view().window()
        popup.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        popup.setStyleSheet(TRANSLATOR_COMBO_BOX_POPUP_STYLE)
        self.view().setContentsMargins(0, 0, 0, 0)
        self.view().setFrameStyle(0)
        self.view().setStyleSheet(TRANSLATOR_COMBO_BOX_LIST_STYLE)
