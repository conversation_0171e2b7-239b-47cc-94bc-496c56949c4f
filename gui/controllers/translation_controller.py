"""
Translation Controller for handling translation operations.
"""

from PyQt6.QtCore import QTimer
from pathlib import Path
import asyncio
from domain.entities.translation import TranslationRequest, TranslationResult


class TranslationController:
    """Controller for handling translation operations."""
    
    def __init__(self, main_window):
        """Initialize the translation controller with reference to main window."""
        self.main_window = main_window
        self.translation_timer = None

        # Enhanced progress tracking
        self._translation_errors = 0
        self._translation_retries = 0
        self._is_cancelled = False
        self._start_time = None

        # Batch processing state
        self.current_batch = 0
        self.total_batches = 0
        self.batch_size = 0
        self.translatable_data = []
        self.translator_service = None
        self.async_loop = None
        self.batch_timer = None
    
    def handle_translate(self):
        """Handle translation with business logic - DEPRECATED - Use handle_excel_translate instead."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info("Translate button clicked (deprecated method)")

        if not (self.main_window.google_translator or self.main_window.deepl_translator):
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error("No translation services configured")
            return

        try:
            # Immediately reset progress bar to 0% when translate button is clicked
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.reset()
                self.main_window.progress_bar.set_progress(0, "Starting translation...")

            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== TRANSLATION STARTED ======")
                self.main_window.business_logger.log_translation_event("started", "Initializing translation workflow", True)

            source_lang = self.main_window.source_language.currentText() if hasattr(self.main_window, 'source_language') else "Auto"
            target_lang = self.main_window.target_language.currentText() if hasattr(self.main_window, 'target_language') else "English"
            translator = self.main_window.translator_combo.currentText() if hasattr(self.main_window, 'translator_combo') else "Google"

            if self.main_window.business_logger:
                self.main_window.business_logger.log_language_change(source_lang, target_lang)
                self.main_window.business_logger.log_translator_change(translator)

            batch_size = 50
            total_cells = 0

            if hasattr(self.main_window, 'batch_size_input'):
                try:
                    batch_size = int(self.main_window.batch_size_input.value())
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Using user-specified batch size: {batch_size}")
                except (ValueError, AttributeError):
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Using default batch size: {batch_size}")

            if not hasattr(self.main_window, 'loaded_file_path') or not self.main_window.loaded_file_path:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning("No file loaded - cannot proceed with translation")
                return

            if hasattr(self.main_window, 'loaded_file_cells') and self.main_window.loaded_file_cells > 0:
                total_cells = self.main_window.loaded_file_cells
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Using file data: {total_cells} translatable cells")
            else:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning("No translatable cells found in loaded file")
                return

            num_batches = (total_cells + batch_size - 1) // batch_size

            if self.main_window.business_logger:
                self.main_window.business_logger.log_batch_calculation(batch_size, total_cells)
                self.main_window.business_logger.log_info(f"Processing {total_cells} cells in {num_batches} batches")

            self._start_translation_process(source_lang, target_lang, translator, batch_size)

        except (ValueError, AttributeError, RuntimeError) as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_translation_event("failed", str(e), False)
    
    def _start_translation_process(self, source_lang, target_lang, translator, batch_size):
        """Start the actual translation process."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Starting file translation process")

            # Reset progress bar to 0% and start incrementing
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.reset()
                self.main_window.progress_bar.set_progress(0, "Initializing translation...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(0, "Initializing translation...")

            # Load Excel file using the business logic
            if self.main_window.loaded_file_path:
                file_path = Path(self.main_window.loaded_file_path)

                # Create timer for async processing simulation
                self.translation_timer = QTimer()
                self.translation_timer.timeout.connect(
                    lambda: self._process_translation_async(file_path, source_lang, target_lang, translator, batch_size)
                )
                self.translation_timer.setSingleShot(True)
                self.translation_timer.start(100)  # Start after 100ms
            else:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error("No file path available for translation")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Failed to start translation: {str(e)}")
    
    def _process_translation_async(self, file_path, source_lang, target_lang, translator, batch_size):
        """Process translation asynchronously."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Processing translation in background")

            # Update progress incrementally starting from 1%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(1, "Loading Excel file...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(1, "Loading Excel file...")

            # Simulate translation workflow
            self._simulate_translation_workflow(source_lang, target_lang, translator, batch_size)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation processing failed: {str(e)}")
    
    def _simulate_translation_workflow(self, source_lang, target_lang, translator, batch_size):
        """Simulate a realistic translation workflow."""
        try:
            # Get translator service with debugging
            translator_service = None
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info(f"Looking for translator: '{translator}'")
                self.main_window.business_logger.log_info(f"Google translator available: {self.main_window.google_translator is not None}")
                self.main_window.business_logger.log_info(f"DeepL translator available: {self.main_window.deepl_translator is not None}")
            
            if translator == "Google Translator" and self.main_window.google_translator:
                translator_service = self.main_window.google_translator
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Using Google Translator - Client initialized: {translator_service.client is not None}")
            elif translator == "DeepL Translator" and self.main_window.deepl_translator:
                translator_service = self.main_window.deepl_translator
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Using DeepL Translator - Client initialized: {translator_service.client is not None}")
            else:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning(f"No matching translator service found for '{translator}'")

            # Check if translator service exists and has valid client
            if not translator_service or not hasattr(translator_service, 'client') or translator_service.client is None:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning(f"Translation service '{translator}' is not available")
                    self.main_window.business_logger.log_info("To enable translation, please configure API keys in Preferences → Settings → API Keys")
                    if translator_service:
                        self.main_window.business_logger.log_info(f"Service status: exists={translator_service is not None}, has_client={hasattr(translator_service, 'client')}, client_valid={getattr(translator_service, 'client', None) is not None}")

                # Reset progress bar to show helpful message
                if hasattr(self.main_window, 'progress_bar'):
                    self.main_window.progress_bar.set_progress(0, "Translation unavailable - Please configure API keys in Preferences")

                # Keep export button always enabled but use default styling after error
                if hasattr(self.main_window, 'export_button'):
                    self.main_window.export_button.setEnabled(True)
                    # Apply default styling (no blue border) after error
                    if hasattr(self.main_window.export_button, 'reset_styles'):
                        self.main_window.export_button.reset_styles()

                # Clear any existing translation results
                if hasattr(self.main_window, 'translation_results'):
                    self.main_window.translation_results = None

                # Show error dialog
                from PyQt6.QtWidgets import QMessageBox
                from gui.styles.dialog_style import MESSAGE_BOX_STYLE

                msg_box = QMessageBox(self.main_window)
                msg_box.setWindowTitle("Translation Failed")
                msg_box.setText(f"Translation service '{translator}' is not available.\n\nPlease configure API keys for translation services in the application settings.")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.setIcon(QMessageBox.Icon.Critical)
                msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
                msg_box.exec()

                return
            
            # Perform translation (real or simulated)
            self._perform_real_translation(translator_service, source_lang, target_lang, batch_size)
            
        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation workflow failed: {str(e)}")
    
    def _perform_real_translation(self, translator_service, source_lang, target_lang, batch_size):
        """Perform actual translation with formatting preservation."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Starting translation with formatting preservation")

            # Update progress incrementally - 2%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(2, "Analyzing file structure...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(2, "Analyzing file structure...")

            # Initialize translation results storage
            self.main_window.translation_results = {}

            # Start actual translation process
            self._start_actual_translation(translator_service, source_lang, target_lang, batch_size)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation execution failed: {str(e)}")

    def _start_actual_translation(self, translator_service, source_lang, target_lang, batch_size):
        """Start the actual translation process with immediate progress updates."""
        try:
            # Get translatable data from the loaded file
            if not self.main_window.loaded_file_path:
                raise Exception("No file loaded for translation")

            # Get selected sheets from the UI
            selected_sheets = []
            if hasattr(self.main_window, 'sheets_scroll'):
                selected_sheets = self.main_window.sheets_scroll.get_selected_sheets()

            if not selected_sheets:
                raise Exception("No sheets selected for translation")

            # Start asynchronous file loading with immediate progress updates
            self._start_async_file_loading(translator_service, source_lang, target_lang, batch_size, selected_sheets)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation process failed: {str(e)}")
            self._handle_translation_error(str(e))

    def _start_async_file_loading(self, translator_service, source_lang, target_lang, batch_size, selected_sheets):
        """Start asynchronous file loading with progress updates."""
        try:
            # Update progress for text extraction - 3%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(3, f"Loading Excel file structure...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(3, f"Loading Excel file structure...")

            # Use timer to break up the file loading process
            self.file_loading_timer = QTimer()
            self.file_loading_timer.timeout.connect(
                lambda: self._load_file_async_step1(translator_service, source_lang, target_lang, batch_size, selected_sheets)
            )
            self.file_loading_timer.setSingleShot(True)
            self.file_loading_timer.start(50)  # Start after 50ms to allow UI update

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Async file loading failed: {str(e)}")
            self._handle_translation_error(str(e))

    def _load_file_async_step1(self, translator_service, source_lang, target_lang, batch_size, selected_sheets):
        """Step 1: Load Excel file asynchronously."""
        try:
            # Update progress - 4%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(4, f"Reading Excel file data...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(4, f"Reading Excel file data...")

            file_path = Path(self.main_window.loaded_file_path)

            # Get or create event loop for translation
            loop = self._get_or_create_event_loop()

            # Load the Excel file
            excel_file = loop.run_until_complete(
                self.main_window.excel_handler.load_file(file_path)
            )

            if not excel_file or not excel_file.is_valid:
                raise Exception("Failed to load Excel file for translation")

            # Continue to next step
            self.file_loading_timer = QTimer()
            self.file_loading_timer.timeout.connect(
                lambda: self._load_file_async_step2(translator_service, source_lang, target_lang, batch_size, selected_sheets, excel_file, loop)
            )
            self.file_loading_timer.setSingleShot(True)
            self.file_loading_timer.start(50)  # Continue after 50ms

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"File loading step 1 failed: {str(e)}")
            self._handle_translation_error(str(e))

    def _load_file_async_step2(self, translator_service, source_lang, target_lang, batch_size, selected_sheets, excel_file, loop):
        """Step 2: Extract translatable data asynchronously."""
        try:
            # Update progress for text extraction - 5%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(5, f"Extracting translatable text from {len(selected_sheets)} selected sheets...")
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(5, f"Extracting translatable text from {len(selected_sheets)} selected sheets...")

            # Get translatable text with formatting for selected sheets only
            translatable_data = loop.run_until_complete(
                self.main_window.excel_handler.get_translatable_text_with_formatting(excel_file, selected_sheets)
            )

            if not translatable_data:
                raise Exception("No translatable content found in file")

            # Continue to next step
            self.file_loading_timer = QTimer()
            self.file_loading_timer.timeout.connect(
                lambda: self._start_translation_batches(translator_service, translatable_data, source_lang, target_lang, batch_size, loop)
            )
            self.file_loading_timer.setSingleShot(True)
            self.file_loading_timer.start(50)  # Continue after 50ms

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"File loading step 2 failed: {str(e)}")
            self._handle_translation_error(str(e))

    def _start_translation_batches(self, translator_service, translatable_data, source_lang, target_lang, batch_size, loop):
        """Start translation batches with progress update."""
        try:
            # Update progress after text extraction with detailed metrics - 7%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(
                    7,
                    f"Found {len(translatable_data)} cells to translate",
                    cells_processed=0,
                    total_cells=len(translatable_data),
                    phase="Text Extraction"
                )
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(7, f"Found {len(translatable_data)} cells to translate")

            # Process translations in batches - keep loop alive
            self._process_translation_batches(
                translator_service, translatable_data, source_lang, target_lang, batch_size, loop
            )

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation batches start failed: {str(e)}")
            self._handle_translation_error(str(e))

    def _process_translation_batches(self, translator_service, translatable_data, source_lang, target_lang, batch_size, loop):
        """Process translation in batches with immediate progress updates."""
        try:
            # Split data into batches
            total_items = len(translatable_data)
            self.total_batches = max(1, (total_items + batch_size - 1) // batch_size)
            self.current_batch = 0

            # Store data for batch processing
            self.translatable_data = translatable_data
            self.translator_service = translator_service
            self.source_lang = source_lang
            self.target_lang = target_lang
            self.batch_size = batch_size
            self.async_loop = loop

            # Update progress to show batch preparation - 8%
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(
                    8,
                    f"Preparing {self.total_batches} translation batches...",
                    cells_processed=0,
                    total_cells=len(translatable_data),
                    phase="Batch Preparation"
                )
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(8, f"Preparing {self.total_batches} translation batches...")

            # Start batch processing with faster timing for immediate feedback
            self.batch_timer = QTimer()
            self.batch_timer.timeout.connect(self._process_translation_batch)
            # Use faster timing for better responsiveness, especially for large files
            timer_interval = max(25, min(100, len(translatable_data) // 50))  # 25-100ms based on data size
            self.batch_timer.start(timer_interval)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Batch processing setup failed: {str(e)}")
            self._handle_translation_error(str(e))
    
    def _process_translation_batch(self):
        """Process a single translation batch."""
        try:
            self.current_batch += 1

            # Calculate progress from 8% to 100% based on translation progress
            # 8% is already used for preparation, so translation uses 8-100% (92% range)
            progress = 8 + int((self.current_batch / self.total_batches) * 92)

            # Get current batch data
            start_idx = (self.current_batch - 1) * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(self.translatable_data))
            batch_data = self.translatable_data[start_idx:end_idx]

            if hasattr(self.main_window, 'progress_bar'):
                # Calculate processed cells up to this batch
                processed_cells = (self.current_batch - 1) * self.batch_size + len(batch_data)

                self.main_window.progress_bar.set_progress(
                    progress,
                    f"Translating batch {self.current_batch}/{self.total_batches} ({len(batch_data)} cells)",
                    cells_processed=processed_cells,
                    total_cells=len(self.translatable_data),
                    phase="Translation"
                )
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(
                        progress,
                        f"Translating batch {self.current_batch}/{self.total_batches} ({len(batch_data)} cells)"
                    )

            # Translate current batch
            self._translate_batch_data(batch_data)

            # Check if all batches are complete
            if self.current_batch >= self.total_batches:
                self.batch_timer.stop()
                self._complete_translation()
                # Clean up async loop
                self._cleanup_async_resources()

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Batch processing failed: {str(e)}")
            if hasattr(self, 'batch_timer'):
                self.batch_timer.stop()
            self._handle_translation_error(str(e))

    def _translate_batch_data(self, batch_data):
        """Translate a batch of data."""
        try:
            # Create translation requests
            requests = []
            for data in batch_data:
                # Process source language
                source_lang_code = "auto"
                if self.source_lang and not ("auto" in self.source_lang.lower() or "detect" in self.source_lang.lower()):
                    source_lang_code = self._get_language_code(self.source_lang)
                
                # Process target language  
                target_lang_code = self._get_language_code(self.target_lang)
                
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Translation request: '{data['original_text'][:30]}...' from '{source_lang_code}' to '{target_lang_code}'")
                
                request = TranslationRequest(
                    text=data['original_text'],
                    source_language=source_lang_code,
                    target_language=target_lang_code,
                    preserve_formatting=True
                )
                requests.append(request)

            # Perform translation with proper async handling
            if hasattr(self.translator_service, 'client') and self.translator_service.client:
                try:
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Translating {len(batch_data)} items with {self.translator_service.service_name}")

                    # Use the existing async loop more efficiently
                    try:
                        # Run the translation using the existing loop
                        results = self.async_loop.run_until_complete(
                            self.translator_service.translate_batch(requests)
                        )
                    except Exception as e:
                        if self.main_window.business_logger:
                            self.main_window.business_logger.log_error(f"Translation batch error: {str(e)}")
                        results = None

                    # Process translation results
                    if results:
                        # Store successful translation results
                        if self.main_window.business_logger:
                            self.main_window.business_logger.log_info(f"Received {len(results)} translation results")

                        for data, result in zip(batch_data, results):
                            if result and not result.error_message and result.translated_text and result.translated_text.strip():
                                # Store the actual translated text
                                self.main_window.translation_results[data['original_text']] = result.translated_text
                                if self.main_window.business_logger:
                                    self.main_window.business_logger.log_info(f"✓ Translated: '{data['original_text'][:30]}...' -> '{result.translated_text[:30]}...'")
                            else:
                                # Track translation errors
                                self._translation_errors += 1

                                # Use original text if translation failed for this specific item
                                self.main_window.translation_results[data['original_text']] = data['original_text']
                                error_msg = result.error_message if result else "No result returned"
                                if self.main_window.business_logger:
                                    self.main_window.business_logger.log_warning(f"✗ Translation failed for: '{data['original_text'][:30]}...' - Error: {error_msg}")

                        # Update progress with error count and log enhanced metrics
                        if hasattr(self.main_window, 'progress_bar'):
                            processed_cells = self.current_batch * self.batch_size
                            self.main_window.progress_bar.set_progress(
                                progress,
                                f"Translating batch {self.current_batch}/{self.total_batches} ({len(batch_data)} cells)",
                                cells_processed=processed_cells,
                                total_cells=len(self.translatable_data),
                                error_count=self._translation_errors,
                                retry_count=self._translation_retries,
                                phase="Translation"
                            )

                            # Log enhanced metrics for debugging
                            if self.main_window.business_logger:
                                metrics = self.main_window.progress_bar.get_enhanced_metrics()
                                if metrics['speed_text']:
                                    self.main_window.business_logger.log_info(f"Translation speed: {metrics['speed_text']}")
                                if metrics['eta_text']:
                                    self.main_window.business_logger.log_info(f"Estimated completion: {metrics['eta_text']}")
                                if metrics['error_text']:
                                    self.main_window.business_logger.log_warning(f"Translation issues: {metrics['error_text']}")
                    else:
                        # Fallback to original text if no results
                        if self.main_window.business_logger:
                            self.main_window.business_logger.log_error("Translation service returned no results")
                        for data in batch_data:
                            self.main_window.translation_results[data['original_text']] = data['original_text']

                except Exception as e:
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_error(f"Translation setup failed: {str(e)}")
                    # Fallback to original text
                    for data in batch_data:
                        self.main_window.translation_results[data['original_text']] = data['original_text']
            else:
                # Fallback: use original text (no translation service available)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning("No translation service available, using original text")
                for data in batch_data:
                    self.main_window.translation_results[data['original_text']] = data['original_text']

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Batch translation failed: {str(e)}")
            # Store original text as fallback
            for data in batch_data:
                self.main_window.translation_results[data['original_text']] = data['original_text']

    def _get_language_code(self, language_name):
        """Convert language name to language code."""
        # Handle empty or None input
        if not language_name or not language_name.strip():
            return "en"

        # Handle both full language names and codes
        language_name_lower = language_name.lower().strip()

        # Comprehensive language mappings including DeepL supported languages
        language_map = {
            # Auto-detect
            "auto-detect": "auto",
            "auto detect": "auto",
            "auto": "auto",
            "automatic": "auto",

            # Major languages supported by DeepL
            "english": "en",
            "japanese": "ja",
            "vietnamese": "vi",
            "chinese": "zh",
            "korean": "ko",
            "spanish": "es",
            "french": "fr",
            "german": "de",
            "italian": "it",
            "portuguese": "pt",
            "russian": "ru",
            "arabic": "ar",
            "hindi": "hi",
            "thai": "th",

            # DeepL supported languages
            "bulgarian": "bg",
            "czech": "cs",
            "danish": "da",
            "greek": "el",
            "estonian": "et",
            "finnish": "fi",
            "hungarian": "hu",
            "lithuanian": "lt",
            "latvian": "lv",
            "dutch": "nl",
            "polish": "pl",
            "romanian": "ro",
            "slovak": "sk",
            "slovenian": "sl",
            "swedish": "sv"
        }

        # Check direct mapping first
        if language_name_lower in language_map:
            return language_map[language_name_lower]

        # Check if it contains key words
        for key, code in language_map.items():
            if key in language_name_lower:
                return code

        # Return the original if it looks like a code (2-3 characters)
        if len(language_name) <= 3:
            return language_name.lower()

        # Default fallback
        return "en"

    def _get_or_create_event_loop(self):
        """Get existing event loop or create a new one safely."""
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
            return loop
        except RuntimeError:
            # Create a new event loop if none exists or current one is closed
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Created new event loop for translation")
                return loop
            except Exception as e:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error(f"Failed to create event loop: {e}")
                raise

    def _cleanup_async_resources(self):
        """Clean up async resources after translation completion."""
        try:
            if hasattr(self, 'async_loop') and self.async_loop and not self.async_loop.is_closed():
                # Don't close the main loop - keep it alive for subsequent operations
                # Only clean up references
                self.async_loop = None
        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error cleaning up async resources: {str(e)}")
    
    def _complete_translation(self):
        """Complete the translation process."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Translation completed - Finalizing results")

            # Translation is complete - finalize immediately (no progress updates for post-processing)
            self._finalize_translation()

            # Try immediate completion first, then use timer as backup
            try:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Attempting immediate completion to 100%")

                # Try direct completion first
                self._finalize_translation()

            except Exception as direct_error:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error(f"Direct completion failed: {str(direct_error)}")

                # Fallback to timer-based completion
                self.completion_timer = QTimer()
                self.completion_timer.timeout.connect(self._finalize_translation)
                self.completion_timer.setSingleShot(True)

                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Using timer-based completion as fallback")

                self.completion_timer.start(500)  # Complete after 0.5 seconds

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation completion failed: {str(e)}")
            self._handle_translation_error(str(e))



    def _translate_excel_file_direct(self, file_path, source_lang, target_lang, translator_type, api_key, progress_callback, selected_sheets=None, batch_size=None):
        """
        Translate Excel file directly using the proper translator plugins.
        This provides direct Excel translation using the proper translator plugins.
        Only processes selected sheets if specified.
        """
        try:
            from openpyxl import load_workbook
            from openpyxl.utils import range_boundaries
            import re

            if progress_callback:
                progress_callback(1, "Loading Excel file...")

            # BACKUP: Create backup before any modifications
            try:
                import shutil
                from pathlib import Path

                backup_path = Path(file_path).with_suffix(f'.backup{Path(file_path).suffix}')
                shutil.copy2(file_path, backup_path)

                if progress_callback:
                    progress_callback(2, f"Created backup: {backup_path.name}")

            except Exception as backup_error:
                if progress_callback:
                    progress_callback(None, f"Warning: Could not create backup: {str(backup_error)}")

            # Load workbook with corruption protection
            try:
                wb = load_workbook(file_path, data_only=False)

                # Immediate validation of loaded workbook
                if not wb.sheetnames:
                    raise Exception("Workbook has no sheets")

                # Test basic operations on each sheet
                for sheet_name in wb.sheetnames:
                    test_sheet = wb[sheet_name]
                    _ = test_sheet.max_row  # This will fail if sheet is corrupted

            except Exception as load_error:
                raise Exception(f"Failed to load Excel file safely: {str(load_error)}")

            if progress_callback:
                progress_callback(3, "Analyzing file structure...")

            # Force UI update
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()

            if progress_callback:
                progress_callback(5, "Initializing translator...")

            # Get the appropriate translator service
            if translator_type.lower() == 'google':
                translator_service = self.main_window.google_translator
            elif translator_type.lower() == 'deepl':
                translator_service = self.main_window.deepl_translator
            else:
                raise ValueError(f"Unknown translator type: {translator_type}")

            if not translator_service or not translator_service.is_available():
                raise Exception(f"{translator_type} translator not available")

            # Filter worksheets to only selected ones
            sheets_to_process = []
            if selected_sheets is not None:
                # Only process selected sheets
                for sheet in wb.worksheets:
                    if sheet.title in selected_sheets:
                        sheets_to_process.append(sheet)
            else:
                # Process all sheets if none specified
                sheets_to_process = wb.worksheets

            if progress_callback:
                progress_callback(15, f"Analyzing {len(sheets_to_process)} selected sheets for translation...")

            # Function to check if text should be preserved (bracket content)
            def should_preserve_text(text):
                text_str = str(text).strip()
                return (text_str.startswith('[') and text_str.endswith(']')) or \
                       (text_str.startswith('(') and text_str.endswith(')')) or \
                       text_str.startswith('=')  # Excel formulas

            # Collect all translatable cells from selected sheets only
            all_translatable_cells = []
            total_sheets = len(sheets_to_process)

            for sheet_index, sheet in enumerate(sheets_to_process):
                if progress_callback:
                    progress_callback(15 + (sheet_index * 5 // total_sheets),
                                    f"Analyzing sheet '{sheet.title}' ({sheet_index + 1}/{total_sheets})...")

                # Force UI update for immediate feedback
                from PyQt6.QtWidgets import QApplication
                QApplication.processEvents()

                # Get all table ranges to avoid translating headers - with corruption prevention
                table_ranges = []
                table_info = {}  # Store table metadata for theme/color preservation

                try:
                    for table_name, tbl in sheet.tables.items():
                        # Validate table reference before processing
                        if tbl.ref and isinstance(tbl.ref, str) and ':' in tbl.ref:
                            try:
                                # Test if range_boundaries can parse this reference
                                min_col, min_row, max_col, max_row = range_boundaries(tbl.ref)

                                # Store valid table range
                                table_ranges.append(tbl.ref)

                                # Preserve table theme and formatting information
                                table_info[table_name] = {
                                    'ref': tbl.ref,
                                    'bounds': (min_col, min_row, max_col, max_row),
                                    'table_style': getattr(tbl, 'tableStyleInfo', None),
                                    'display_name': getattr(tbl, 'displayName', table_name),
                                    'header_row_count': getattr(tbl, 'headerRowCount', 1),
                                    'totals_row_count': getattr(tbl, 'totalsRowCount', 0)
                                }
                            except (ValueError, TypeError, AttributeError) as e:
                                # Skip malformed table references to prevent corruption
                                if progress_callback:
                                    progress_callback(None, f"Warning: Skipping malformed table '{table_name}' in sheet '{sheet.title}': {str(e)}")
                                continue
                except (AttributeError, TypeError):
                    # Handle case where sheet.tables is None or not iterable
                    pass

                def is_in_table_header(cell):
                    """Check if cell is in table header while preserving table formatting."""
                    try:
                        for table_ref in table_ranges:
                            try:
                                min_col, min_row, max_col, max_row = range_boundaries(table_ref)
                                # Check if cell is in header row (first row of table)
                                if cell.row == min_row and min_col <= cell.column <= max_col:
                                    return True
                            except (ValueError, TypeError):
                                # Skip invalid table reference
                                continue
                        return False
                    except Exception:
                        # Fail safe - if we can't determine, don't translate to preserve formatting
                        return True

                # Count translatable cells
                sheet_cells = []
                for row in sheet.iter_rows():
                    for cell in row:
                        if isinstance(cell.value, str) and not is_in_table_header(cell):
                            if not should_preserve_text(cell.value):
                                sheet_cells.append((cell, sheet.title))

                all_translatable_cells.extend(sheet_cells)

                if progress_callback:
                    progress_callback(15 + ((sheet_index + 1) * 5 // total_sheets),
                                    f"Found {len(sheet_cells)} translatable cells in '{sheet.title}'")

            total_cells = len(all_translatable_cells)

            if progress_callback:
                progress_callback(20, f"Starting translation of {total_cells} cells across {total_sheets} sheets...")

            # Translate cells in batches using user-specified or default batch size
            if batch_size is None:
                batch_size = 50  # Default batch size
            total_batches = (total_cells + batch_size - 1) // batch_size if total_cells > 0 else 1

            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, total_cells)
                batch_cells = all_translatable_cells[start_idx:end_idx]

                if progress_callback:
                    batch_progress = 20 + (batch_index * 65 // max(total_batches, 1))
                    progress_callback(min(batch_progress, 85),
                                    f"Processing batch {batch_index + 1}/{total_batches} ({len(batch_cells)} cells)")

                # Force UI update for immediate feedback
                from PyQt6.QtWidgets import QApplication
                QApplication.processEvents()

                # Prepare translation requests for this batch
                from domain.entities.translation import TranslationRequest
                requests = []
                cells_to_update = []

                for cell, sheet_title in batch_cells:
                    original_text = str(cell.value)
                    requests.append(TranslationRequest(
                        text=original_text,
                        source_language=source_lang,
                        target_language=target_lang
                    ))
                    cells_to_update.append(cell)

                # Translate the batch
                if requests:
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        results = loop.run_until_complete(
                            translator_service.translate_batch(requests)
                        )

                        # Apply translations to cells
                        for cell, result in zip(cells_to_update, results):
                            if result and not result.error_message and result.translated_text:
                                cell.value = result.translated_text

                        loop.close()

                        # Force UI update after each batch
                        QApplication.processEvents()

                    except Exception as e:
                        if progress_callback:
                            progress_callback(min(batch_progress, 85),
                                            f"Batch {batch_index + 1} failed: {str(e)}")
                        # Keep original text if translation fails
                        pass

            if progress_callback:
                progress_callback(95, "Validating translated file integrity...")

            # CRITICAL: Validate file integrity before returning
            try:
                # Create a temporary copy to test file integrity
                import tempfile
                import os
                import openpyxl
                from openpyxl.utils import range_boundaries

                with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                    temp_path = temp_file.name

                # Test save to temporary file first
                wb.save(temp_path)

                # Validate the saved file can be reopened
                test_wb = openpyxl.load_workbook(temp_path, data_only=False)

                # Verify all sheets are accessible
                for sheet_name in test_wb.sheetnames:
                    test_sheet = test_wb[sheet_name]
                    # Test basic operations to ensure no corruption
                    _ = test_sheet.max_row
                    _ = test_sheet.max_column

                    # Verify table integrity if tables exist
                    if hasattr(test_sheet, 'tables') and test_sheet.tables:
                        tables_to_remove = []

                        try:
                            for table_name, table in test_sheet.tables.items():
                                try:
                                    # Check if table object has ref attribute and it's valid
                                    if hasattr(table, 'ref') and table.ref:
                                        if isinstance(table.ref, str) and ':' in table.ref:
                                            range_boundaries(table.ref)
                                        else:
                                            tables_to_remove.append(table_name)
                                    else:
                                        tables_to_remove.append(table_name)
                                except Exception:
                                    tables_to_remove.append(table_name)

                            # Remove corrupted tables
                            for table_name in tables_to_remove:
                                try:
                                    del test_sheet.tables[table_name]
                                    if progress_callback:
                                        progress_callback(None, f"Warning: Removed corrupted table '{table_name}' from sheet '{sheet_name}'")
                                except:
                                    pass

                        except Exception:
                            # If table iteration fails completely, skip table validation
                            pass

                test_wb.close()

                # Clean up temporary file
                os.unlink(temp_path)

                if progress_callback:
                    progress_callback(100, "Translation completed and validated!")

                return wb

            except Exception as validation_error:
                # Clean up temporary file if it exists
                if 'temp_path' in locals() and os.path.exists(temp_path):
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

                if progress_callback:
                    progress_callback(0, f"File validation failed: {str(validation_error)}")

                # If validation fails, still return the workbook but with warning
                # This prevents losing translation work due to minor validation issues
                if progress_callback:
                    progress_callback(100, f"Translation completed with warnings: {str(validation_error)}")

                return wb

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"Translation failed: {str(e)}")
            raise e

    def _handle_translation_error(self, error_message):
        """Handle translation errors."""
        try:
            # Set progress bar to error state
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_error_state(f"Translation failed: {error_message}")

            # Show error message
            from PyQt6.QtWidgets import QMessageBox
            from gui.styles.dialog_style import MESSAGE_BOX_STYLE

            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("Translation Failed")
            msg_box.setText(f"Translation process failed:\n{error_message}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error handling failed: {str(e)}")

    def cancel_translation(self):
        """Cancel the current translation process."""
        try:
            self._is_cancelled = True

            # Stop batch timer if running
            if self.batch_timer and self.batch_timer.isActive():
                self.batch_timer.stop()

            # Set progress bar to cancelled state
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_cancelled_state()

            # Log cancellation
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Translation cancelled by user")
                self.main_window.business_logger.log_status_message("====== TRANSLATION CANCELLED ======")

            # Reset translation state
            self._reset_translation_state()

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Cancellation failed: {str(e)}")

    def _reset_translation_state(self):
        """Reset translation state variables."""
        self._translation_errors = 0
        self._translation_retries = 0
        self._is_cancelled = False
        self._start_time = None
        self.current_batch = 0
        self.total_batches = 0
        self.translatable_data = []

    def _reset_translation_for_new_process(self):
        """Reset everything for a fresh translation process."""
        try:
            # Reset progress bar to 0% and ready state
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.reset()
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Progress bar reset to 0% for new translation")

            # Reset translation state variables
            self._reset_translation_state()

            # Clear any previous translation results
            if hasattr(self.main_window, 'translation_results'):
                self.main_window.translation_results = {}

            # Clear any previous translated workbook
            if hasattr(self.main_window, 'translated_workbook'):
                self.main_window.translated_workbook = None

            # Keep export button always enabled but use default styling during reset
            if hasattr(self.main_window, 'export_button'):
                self.main_window.export_button.setEnabled(True)
                # Apply default styling (no blue border) during reset
                if hasattr(self.main_window.export_button, 'reset_styles'):
                    self.main_window.export_button.reset_styles()

            # Stop any running batch timer
            if self.batch_timer and self.batch_timer.isActive():
                self.batch_timer.stop()

            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Translation state reset for new process")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error resetting translation state: {str(e)}")

    def _prepare_for_new_translation(self):
        """Prepare the application for a new translation after completion or cancellation."""
        try:
            # Reset progress bar to ready state
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.reset()

            # Enable translate button for new translation
            if hasattr(self.main_window, 'translate_button'):
                self.main_window.translate_button.setEnabled(True)

            # Keep export button always enabled but use default styling after cancellation
            if hasattr(self.main_window, 'export_button'):
                self.main_window.export_button.setEnabled(True)
                # Apply default styling (no blue border) after cancellation
                if hasattr(self.main_window.export_button, 'reset_styles'):
                    self.main_window.export_button.reset_styles()
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Export button enabled with default styling (no fresh results)")

            # Clear translation state
            self._reset_translation_state()

            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Application prepared for new translation")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error preparing for new translation: {str(e)}")

    def _finalize_translation(self):
        """Finalize the translation process."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== TRANSLATION COMPLETED ======")
                self.main_window.business_logger.log_info("Finalization method called - updating to 100%")

            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_completed_state()
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_progress_update(100, "Translation completed successfully")
                    self.main_window.business_logger.log_translation_event("completed", "File translation finished", True)

            # Enable export button only if translation results are available
            if hasattr(self.main_window, 'export_button'):
                if hasattr(self.main_window, 'translation_results') and self.main_window.translation_results:
                    self.main_window.export_button.setEnabled(True)
                    # Apply blue border styling to indicate export is available
                    if hasattr(self.main_window.export_button, 'apply_enabled_styles'):
                        self.main_window.export_button.apply_enabled_styles()
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info("Export functionality enabled")
                else:
                    # Keep export button always enabled but use default styling without results
                    self.main_window.export_button.setEnabled(True)
                    # Apply default styling (no blue border) when no fresh results
                    if hasattr(self.main_window.export_button, 'reset_styles'):
                        self.main_window.export_button.reset_styles()
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info("Export button enabled with default styling (no fresh results)")

            # Clean up completion timer
            if hasattr(self, 'completion_timer'):
                self.completion_timer.stop()
                self.completion_timer = None

            # Ensure translate button is enabled for new translations
            if hasattr(self.main_window, 'translate_button'):
                self.main_window.translate_button.setEnabled(True)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Translate button enabled for new translation")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Translation finalization failed: {str(e)}")
    
    def handle_cancel_translation(self):
        """Handle translation cancellation and reset application state."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_status_message("====== TRANSLATION CANCELLED ======")
            self.main_window.business_logger.log_info("Translation cancelled by user - Resetting application")

        # Use the enhanced cancellation method
        self.cancel_translation()

        # Ensure everything is ready for a new translation
        self._prepare_for_new_translation()

        # Stop any running timers
        if hasattr(self, 'translation_timer') and self.translation_timer:
            self.translation_timer.stop()
        if hasattr(self, 'batch_timer') and self.batch_timer:
            self.batch_timer.stop()
        if hasattr(self, 'completion_timer') and self.completion_timer:
            self.completion_timer.stop()

        # Reset application state (preserve language and API keys)
        self._reset_application_state()

        # Reset progress bar immediately to initial state (no cancel message in progress bar)
        self._complete_application_reset()

    def _reset_application_state(self):
        """Reset application to initial state while preserving language and API keys."""
        try:
            # Clear file-related data
            self.main_window.loaded_file_path = None
            self.main_window.loaded_file_cells = 0
            self.main_window.loaded_sheets = []

            # Clear translation results
            if hasattr(self.main_window, 'translation_results'):
                self.main_window.translation_results = None

            # Reset file upload component
            if hasattr(self.main_window, 'file_upload'):
                self.main_window.file_upload.reset_state()

            # Reset sheets display
            if hasattr(self.main_window, 'sheets_scroll'):
                self.main_window.sheets_scroll.clear_sheets()

            # Reset combo boxes to default values (but keep language settings)
            if hasattr(self.main_window, 'source_language'):
                # Find "Auto Detect" or similar in current language
                for i in range(self.main_window.source_language.count()):
                    item_text = self.main_window.source_language.itemText(i)
                    if "Auto" in item_text or "自動" in item_text or "Tự động" in item_text:
                        self.main_window.source_language.setCurrentIndex(i)
                        break

            if hasattr(self.main_window, 'target_language'):
                # Find "English" in current language
                for i in range(self.main_window.target_language.count()):
                    item_text = self.main_window.target_language.itemText(i)
                    if "English" in item_text or "英語" in item_text or "Tiếng Anh" in item_text:
                        self.main_window.target_language.setCurrentIndex(i)
                        break

            if hasattr(self.main_window, 'translator_combo'):
                # Reset to first available translator
                if self.main_window.translator_combo.count() > 0:
                    self.main_window.translator_combo.setCurrentIndex(0)

            if hasattr(self.main_window, 'batch_size_input'):
                # Reset batch size to default (50)
                self.main_window.batch_size_input.setValue(50)

            # Keep export button always enabled but use default styling during complete reset
            if hasattr(self.main_window, 'export_button'):
                self.main_window.export_button.setEnabled(True)
                # Apply default styling (no blue border) during complete reset
                if hasattr(self.main_window.export_button, 'reset_styles'):
                    self.main_window.export_button.reset_styles()

            # Clear logs (but keep welcome message)
            if hasattr(self.main_window, 'log_widget'):
                self.main_window.log_widget.clear()

            # Re-initialize business logger and show welcome message
            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== WELCOME TO EXCEL TRANSLATOR ======")

                # Log available translation services
                if hasattr(self.main_window, 'ui_builder'):
                    self.main_window.ui_builder._log_default_translator_initialization()

            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Application state reset completed")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error during application reset: {e}")

    def _complete_application_reset(self):
        """Complete the application reset by setting progress bar to initial state."""
        try:
            from ..managers.localization_manager import tr

            # Set progress bar back to initial state
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(0, tr("progress_title"))

            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Application reset completed - Ready for new translation")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error completing application reset: {e}")

    def handle_excel_translate(self):
        """Handle translation using the Excel translation logic provided by the user."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info("Excel translate button clicked")

        # Reset progress bar and translation state at the start of each translation
        self._reset_translation_for_new_process()

        if not (self.main_window.google_translator or self.main_window.deepl_translator):
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error("No translation services configured")
            return

        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== EXCEL TRANSLATION STARTED ======")

            # Get language settings
            source_lang_text = self.main_window.source_language.currentText() if hasattr(self.main_window, 'source_language') else "auto"
            target_lang_text = self.main_window.target_language.currentText() if hasattr(self.main_window, 'target_language') else "en"
            translator_type = self.main_window.translator_combo.currentText() if hasattr(self.main_window, 'translator_combo') else "Google"

            # Convert language display names to language codes
            source_lang = self._get_language_code(source_lang_text)
            target_lang = self._get_language_code(target_lang_text)

            if self.main_window.business_logger:
                self.main_window.business_logger.log_info(f"Language mapping: '{source_lang_text}' -> '{source_lang}', '{target_lang_text}' -> '{target_lang}'")

            # Get selected sheets from the UI
            selected_sheets = []
            if hasattr(self.main_window, 'sheets_scroll'):
                selected_sheets = self.main_window.sheets_scroll.get_selected_sheets()

            if not selected_sheets:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error("No sheets selected for translation")
                return

            # Get file path
            if not self.main_window.loaded_file_path:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error("No file loaded for translation")
                return

            file_path = self.main_window.loaded_file_path

            # Update progress
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(10, f"Starting translation of {len(selected_sheets)} selected sheets...")

            # Get API key based on translator type
            api_key = None
            selected_translator_type = None

            if "google" in translator_type.lower():
                api_key = self.main_window.google_translator.api_key if self.main_window.google_translator else None
                selected_translator_type = 'google'
            elif "deepl" in translator_type.lower():
                api_key = self.main_window.deepl_translator.api_key if self.main_window.deepl_translator else None
                selected_translator_type = 'deepl'

            if not api_key:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning(f"No API key configured for {translator_type}")
                    self.main_window.business_logger.log_info("Please add API keys in Preferences → Settings → API Keys to enable translation")
                return

            if not selected_translator_type:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error(f"Unknown translator type: {translator_type}")
                return

            # Use the proper translator plugins directly
            from openpyxl import load_workbook
            import asyncio

            # Create progress callback function with immediate updates
            def progress_callback(progress: int, message: str):
                if hasattr(self.main_window, 'progress_bar'):
                    self.main_window.progress_bar.set_progress(progress, message)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Progress: {progress}% - {message}")
                # Force UI update for immediate feedback
                from PyQt6.QtWidgets import QApplication
                QApplication.processEvents()

            # Start with immediate progress update
            progress_callback(0, "Starting translation...")

            # Get user-specified batch size
            user_batch_size = 50  # Default
            if hasattr(self.main_window, 'batch_size_input'):
                try:
                    user_batch_size = int(self.main_window.batch_size_input.value())
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Using user-specified batch size: {user_batch_size}")
                except (ValueError, AttributeError):
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Using default batch size: {user_batch_size}")

            # Perform translation using the existing translation workflow
            translated_wb = self._translate_excel_file_direct(
                file_path=file_path,
                source_lang=source_lang,
                target_lang=target_lang,
                translator_type=selected_translator_type,
                api_key=api_key,
                progress_callback=progress_callback,
                selected_sheets=selected_sheets,
                batch_size=user_batch_size
            )

            # Store the translated workbook for export
            self.main_window.translated_workbook = translated_wb

            # Update progress
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_completed_state()

            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== SIMPLE TRANSLATION COMPLETED ======")

            # Enable export button with blue border styling after translation completion
            if hasattr(self.main_window, 'export_button'):
                self.main_window.export_button.setEnabled(True)
                # Apply blue border styling to match file browse button
                if hasattr(self.main_window.export_button, 'apply_enabled_styles'):
                    self.main_window.export_button.apply_enabled_styles()

            # Ensure translate button is enabled for new translations
            if hasattr(self.main_window, 'translate_button'):
                self.main_window.translate_button.setEnabled(True)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Simple translation failed: {str(e)}")
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_error_state(f"Translation failed: {str(e)}")

            # Prepare for new translation even after failure
            self._prepare_for_new_translation()
