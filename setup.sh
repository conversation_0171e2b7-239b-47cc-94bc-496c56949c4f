#!/bin/bash
"""
Setup script for Excel Translator project.
"""

echo "🚀 Setting up Excel Translator project..."

# Check if Python 3.12+ is available
python_version=$(python3 --version 2>&1 | cut -d" " -f2 | cut -d"." -f1,2)
required_version="3.12"

if [[ $(echo "$python_version < $required_version" | bc -l) -eq 1 ]]; then
    echo "❌ Python 3.12+ is required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p config

# Copy environment template if .env doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating .env file..."
    cp .env .env.backup 2>/dev/null || true
    cat > .env << EOF
# Environment variables for Translation App

# Google Cloud Translation API
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# DeepL Translation API
DEEPL_API_KEY=your_deepl_api_key_here

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
MAX_BATCH_SIZE=100
DEFAULT_SOURCE_LANGUAGE=auto
DEFAULT_TARGET_LANGUAGE=en

# File Processing Settings
MAX_FILE_SIZE_MB=50
SUPPORTED_FORMATS=xlsx,xls
EOF
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi

# Validate installation
echo "🔍 Validating installation..."
python3 -c "
try:
    import PyQt6
    import openpyxl
    import pandas
    import requests
    print('✅ Core packages imported successfully')
except ImportError as e:
    print(f'❌ Import error: {e}')
    exit(1)
"

# Test core GUI
echo "🖥️ Testing GUI..."
python3 -c "
import sys
from PyQt6.QtWidgets import QApplication
app = QApplication(sys.argv)
print('✅ GUI framework test passed')
app.quit()
"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your API keys:"
echo "   - Get Google Translate API key: https://console.cloud.google.com/"
echo "   - Get DeepL API key: https://www.deepl.com/pro"
echo ""
echo "2. Run the application:"
echo "   python3 main.py          # Excel Translator application"
echo ""
echo "3. Read README.md for detailed usage instructions"
echo ""
echo "🔧 Troubleshooting:"
echo "   - Check logs/ directory for error logs"
echo "   - Run: python3 -m pytest tests/ for testing"
echo "   - Open an issue on GitHub for support"
echo ""
