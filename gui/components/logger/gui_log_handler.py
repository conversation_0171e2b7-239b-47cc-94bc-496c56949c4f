"""
GUI Logging Handler - Connects Python logging to Qt GUI log widget
"""

import logging
from PyQt6.QtCore import QObject, pyqtSignal


class Gui<PERSON>ogHandler(logging.Handler, QObject):
    """Custom logging handler that emits logs to GUI widget"""

    log_message = pyqtSignal(str, str)
    
    def __init__(self, log_widget=None):
        logging.Handler.__init__(self)
        QObject.__init__(self)
        
        self.log_widget = log_widget
        if self.log_widget:
            self.log_message.connect(self.log_widget.add_log)
    
    def emit(self, record):
        """Emit a log record to the GUI widget"""
        try:
            msg = self.format(record)

            level_map = {
                logging.DEBUG: "DEBUG",
                logging.INFO: "INFO",
                logging.WARNING: "WARNING",
                logging.ERROR: "ERROR",
                logging.CRITICAL: "CRITICAL"
            }

            level = level_map.get(record.levelno, "INFO")
            self.log_message.emit(msg, level)

        except Exception:
            pass
    
    def set_log_widget(self, log_widget):
        """Set or update the log widget"""
        self.log_widget = log_widget
        if self.log_widget:
            self.log_message.connect(self.log_widget.add_log)


class BusinessLogicLogger:
    """Centralized logger for business logic events"""
    
    def __init__(self, log_widget=None):
        self.log_widget = log_widget
        self.component_name = "BUSINESS"
    
    def set_log_widget(self, log_widget):
        """Set the GUI log widget"""
        self.log_widget = log_widget
    
    def log_file_operation(self, operation, file_path, success=True):
        """Log file operations"""
        if self.log_widget:
            if success:
                if operation == "load":
                    message = f"Successfully loaded file: {file_path}"
                elif operation == "save":
                    message = f"Successfully saved file: {file_path}"
                elif operation == "export":
                    message = f"Successfully exported file: {file_path}"
                else:
                    message = f"File {operation} completed: {file_path}"
                level = "success"
            else:
                if operation == "load":
                    message = f"Failed to load file: {file_path}"
                elif operation == "save":
                    message = f"Failed to save file: {file_path}"
                elif operation == "export":
                    message = f"Failed to export file: {file_path}"
                else:
                    message = f"File {operation} failed: {file_path}"
                level = "error"

            self.log_widget.add_log(message, level)
    
    def log_translation_event(self, event, details="", success=True):
        """Log translation events"""
        if self.log_widget:
            message = f"Translation {event}: {details}"
            level = "success" if success else "error"
            self.log_widget.add_log(message, level)
    
    def log_language_change(self, source_lang, target_lang):
        """Log language selection changes"""
        if self.log_widget:
            message = f"Language changed: {source_lang} → {target_lang}"
            self.log_widget.add_log(message, "info")
    
    def log_translator_change(self, translator):
        """Log translator service changes"""
        if self.log_widget:
            message = f"Translator changed to: {translator}"
            self.log_widget.add_log(message, "info")
    
    def log_progress_update(self, progress, message):
        """Log progress updates"""
        if self.log_widget:
            log_message = f"Progress {progress}%: {message}"
            self.log_widget.add_log(log_message, "info")
    
    def log_error(self, error_msg, component="APP"):
        """Log error messages"""
        if self.log_widget:
            self.log_widget.add_log(error_msg, "error")

    def log_warning(self, warning_msg, component="APP"):
        """Log warning messages"""
        if self.log_widget:
            self.log_widget.add_log(warning_msg, "warning")

    def log_info(self, info_msg, component="APP"):
        """Log info messages"""
        if self.log_widget:
            self.log_widget.add_log(info_msg, "info")
    
    def log_batch_calculation(self, batch_size, total_cells):
        """Log batch size calculations"""
        if self.log_widget:
            message = f"Auto-calculated optimal batch size: {batch_size} for {total_cells} cells"
            self.log_widget.add_log(message, "info")
    
    def log_api_initialization(self, api_name, success=True):
        """Log API service initialization"""
        if self.log_widget:
            status = "initialized successfully" if success else "initialization failed"
            message = f"{api_name} client {status}"
            level = "success" if success else "error"
            self.log_widget.add_log(message, level)

    def log_status_message(self, message):
        """Log status messages with special formatting and color"""
        if self.log_widget:
            # Determine status type based on message content
            if "WELCOME" in message:
                level = "status_welcome"
            elif "STARTED" in message:
                level = "status_started"
            elif "CANCELLED" in message:
                level = "status_cancelled"
            elif "COMPLETED" in message:
                level = "status_completed"
            elif "EXPORTED" in message:
                level = "status_exported"
            elif "LOADED" in message:
                level = "status_loaded"
            else:
                level = "status_general"

            self.log_widget.add_log(message, level)
