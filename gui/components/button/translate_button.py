
from PyQt6.QtWidgets import <PERSON><PERSON>ush<PERSON><PERSON>on
from ...styles.translate_button_style import TRANSLATE_BUTTON_STYLE
from ...managers.localization_manager import get_localization_manager, tr


class TranslateButton(QPushButton):
    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_text)

    def setup_ui(self):
        self.setText(tr("translate_button"))
        self.setFixedHeight(40)
        self.setMinimumWidth(100)

    def apply_styles(self):
        self.setStyleSheet(TRANSLATE_BUTTON_STYLE)

    def update_text(self):
        """Update button text when language changes."""
        self.setText(tr("translate_button"))
