# Excel Translator Application

A comprehensive Qt-based GUI application for translating Excel files using Google Translate and DeepL APIs. Built with clean architecture principles and modern Python practices.

## Features

### File Handling
- Support for Excel formats `.xlsx` and `.xls`
- Automatic detection of all sheets and translatable text cells
- Preserve original formatting, design, and structure
- Backup and restore functionality
- External configuration file support

### Translation
- Google Cloud Translate API integration
- DeepL API integration
- Automatic language detection
- Batch processing with configurable batch sizes
- Preserve text in brackets `[]` and Japanese quotation marks `「」`
- Format preservation (fonts, colors, borders, alignment, etc.)

### User Interface
- Modern Qt6-based GUI with dark theme
- Drag-and-drop file upload
- Real-time progress tracking
- Comprehensive logging and error handling
- Multi-language support (Japanese, Vietnamese, English)
- Intuitive workflow design

### Performance & Scalability
- Asynchronous processing to prevent UI freezing
- Intelligent batching for API optimization
- Support for large files (10,000+ rows)
- Background processing
- Rate limiting and retry mechanisms

## Project Structure

```
Translator-3/
├── main.py                     # Main application entry point
├── run_lightweight.py         # Lightweight runner without full dependencies
├── requirements.txt           # Python dependencies
├── .env                       # Environment variables (API keys)
├── README.md                  # This file
│
├── application/               # Application layer (Use cases, DTOs)
│   ├── dto/                   # Data transfer objects
│   └── usecases/              # Business use cases
│
├── domain/                    # Domain layer (Entities, Repository interfaces)
│   ├── entities/              # Domain entities
│   └── repositories/          # Repository interfaces
│
├── infrastructure/            # Infrastructure layer (External services)
│   ├── file_handlers/         # File processing implementations
│   ├── plugins/               # Translation service plugins
│   └── validators/            # Validation implementations
│
├── interfaces/                # Interfaces layer (Contracts)
│   ├── repositories/          # Repository interfaces
│   ├── services/              # Service interfaces
│   └── validators/            # Validator interfaces
│
├── gui/                       # GUI layer (Qt components)
│   ├── components/            # Reusable UI components
│   ├── dialogs/               # Dialog windows
│   ├── styles/                # CSS/QSS styling
│   └── windows/               # Main windows
│
├── helpers/                   # Helper utilities
├── config/                    # Configuration files
└── logs/                      # Application logs (generated)
```

## Installation

### Prerequisites
- Python 3.12 or higher
- pip package manager

### Setup Steps

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd Translator-3
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\\Scripts\\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys
   ```

5. **Setup API keys:**
   - **Google Cloud Translate:** Get API key from [Google Cloud Console](https://console.cloud.google.com/)
   - **DeepL:** Get API key from [DeepL Pro](https://www.deepl.com/pro)

   Update `.env` file:
   ```bash
   GOOGLE_TRANSLATE_API_KEY=your_google_api_key_here
   DEEPL_API_KEY=your_deepl_api_key_here
   ```

## Usage

### Running the Application

#### Full Application (with all features):
```bash
python main.py
```

#### Lightweight UI-only version:
```bash
python run_lightweight.py
```

### Basic Workflow

1. **Start the application**
2. **Upload Excel file** via drag-and-drop or file selector
3. **Select source and target languages**
4. **Choose translation engine** (Google Translate or DeepL)
5. **Configure batch size** for optimal performance
6. **Select sheets to translate**
7. **Click "Translate"** to start the process
8. **Monitor progress** in real-time
9. **Export translated file** when complete

### Advanced Features

#### Batch Processing
- Adjust batch size based on your API limits
- Monitor API usage and rate limits
- Automatic retry on failures

#### Configuration Files
Create external configuration files to define:
- Files and sheets to translate
- Language pairs
- Processing settings
- API preferences

#### Logging
All operations are logged for:
- Debugging and troubleshooting
- Performance monitoring
- Audit trails
- Error analysis

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_TRANSLATE_API_KEY` | Google Cloud Translate API key | Required |
| `DEEPL_API_KEY` | DeepL API key | Required |
| `DEFAULT_TRANSLATION_ENGINE` | Default translation service | `google` |
| `MAX_BATCH_SIZE` | Maximum batch size | `100` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `MAX_FILE_SIZE_MB` | Maximum file size in MB | `50` |

### Settings File

The application supports JSON configuration files for advanced settings:

```json
{
  "api": {
    "default_translation_engine": "google",
    "request_timeout": 30,
    "max_retries": 3
  },
  "translation": {
    "default_batch_size": 50,
    "preserve_formatting": true,
    "ignore_brackets": true
  },
  "ui": {
    "theme": "dark",
    "language": "en",
    "window_width": 1200,
    "window_height": 800
  }
}
```

## API Rate Limits

### Google Cloud Translate
- **Free tier:** 500,000 characters/month
- **Paid tier:** Up to 1,000 requests/minute
- **Characters per request:** 30,000

### DeepL
- **Free tier:** 500,000 characters/month
- **Pro tier:** Varies by plan
- **Characters per request:** 10,000

## Supported Languages

The application supports all languages available in Google Translate and DeepL:

### Common Language Pairs
- English ↔ Japanese
- English ↔ Vietnamese
- Japanese ↔ Vietnamese
- English ↔ Spanish/French/German
- And many more...

## Error Handling

### Common Issues and Solutions

1. **API Key Errors**
   - Verify API keys in `.env` file
   - Check API key permissions and quotas

2. **File Format Errors**
   - Ensure file is `.xlsx` or `.xls` format
   - Check file is not corrupted

3. **Network Errors**
   - Check internet connection
   - Verify firewall settings

4. **Memory Issues**
   - Reduce batch size for large files
   - Close other applications

## Development

### Architecture

The application follows Clean Architecture principles:

- **Domain Layer:** Core business entities and rules
- **Application Layer:** Use cases and application logic
- **Infrastructure Layer:** External service implementations
- **Interface Layer:** Contracts and interfaces
- **GUI Layer:** User interface components

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Testing

```bash
# Run unit tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the logs in `logs/app.log`
- Review the troubleshooting section

## Changelog

### Version 2.0.0
- Complete rewrite with clean architecture
- Added DeepL support
- Improved error handling and logging
- Enhanced UI with real-time progress
- Better formatting preservation
- Asynchronous processing

### Version 1.0.0
- Initial release
- Basic Excel translation functionality
- Google Translate integration
- Clean Qt GUI


---

gui/controllers/translation_controller.py

line 784 - 800, fix an appropriate so that file not able to corrupt. But must preserve original table theme/color or excel theme/color?
