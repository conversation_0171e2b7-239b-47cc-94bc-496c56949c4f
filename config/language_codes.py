"""
Language codes and mappings for translation services.
"""

from typing import Dict, List, Optional


# Supported language codes - Japanese, Vietnamese, and English only
LANGUAGE_CODES = {
    'auto': 'Auto-Detect',
    'en': 'English',
    'ja': 'Japanese',
    'vi': 'Vietnamese'
}


# Google Translate supported languages (limited to Japanese, Vietnamese, English)
GOOGLE_SUPPORTED_LANGUAGES = ['auto', 'en', 'ja', 'vi']

# DeepL supported languages (limited to Japanese, Vietnamese, English)
DEEPL_SUPPORTED_LANGUAGES = ['en', 'ja', 'vi']

# Language aliases and variations
LANGUAGE_ALIASES = {
    'english': 'en',
    'japanese': 'ja',
    'vietnamese': 'vi'
}

# Common language pairs for quick selection (limited to supported languages)
COMMON_LANGUAGE_PAIRS = [
    ('en', 'vi'),  # English to Vietnamese
    ('en', 'ja'),  # English to Japanese
    ('ja', 'en'),  # Japanese to English
    ('ja', 'vi'),  # Japanese to Vietnamese
    ('vi', 'en'),  # Vietnamese to English
    ('vi', 'ja'),  # Vietnamese to Japanese
]


def get_language_name(code: str) -> str:
    """Get language name from code."""
    return LANGUAGE_CODES.get(code.lower(), code)


def get_language_code(name_or_code: str) -> Optional[str]:
    """Get language code from name or alias."""
    name_or_code = name_or_code.lower().strip()
    
    # Check if it's already a valid code
    if name_or_code in LANGUAGE_CODES:
        return name_or_code
    
    # Check aliases
    if name_or_code in LANGUAGE_ALIASES:
        return LANGUAGE_ALIASES[name_or_code]
    
    # Search by name
    for code, name in LANGUAGE_CODES.items():
        if name.lower() == name_or_code:
            return code
    
    return None


def is_supported_by_google(language_code: str) -> bool:
    """Check if language is supported by Google Translate."""
    return language_code.lower() in GOOGLE_SUPPORTED_LANGUAGES


def is_supported_by_deepl(language_code: str) -> bool:
    """Check if language is supported by DeepL."""
    return language_code.lower() in DEEPL_SUPPORTED_LANGUAGES


def get_supported_languages(service: str = 'all') -> Dict[str, str]:
    """Get supported languages for a service."""
    if service.lower() == 'google':
        return {code: LANGUAGE_CODES[code] for code in GOOGLE_SUPPORTED_LANGUAGES if code in LANGUAGE_CODES}
    elif service.lower() == 'deepl':
        return {code: LANGUAGE_CODES[code] for code in DEEPL_SUPPORTED_LANGUAGES if code in LANGUAGE_CODES}
    else:
        return LANGUAGE_CODES


def get_common_languages() -> Dict[str, str]:
    """Get commonly used languages (all supported languages)."""
    return LANGUAGE_CODES


def normalize_language_code(code: str) -> str:
    """Normalize language code to standard format."""
    if not code:
        return 'auto'

    normalized = code.lower().strip()

    # Handle common variations for supported languages
    variations = {
        'en-us': 'en',
        'en-gb': 'en',
        'ja-jp': 'ja',
        'vi-vn': 'vi'
    }

    if normalized in variations:
        return variations[normalized]

    # Take first two characters for standard codes
    if len(normalized) > 2 and '-' in normalized:
        return normalized.split('-')[0]

    return normalized[:2] if len(normalized) >= 2 else normalized


def get_language_direction(source_code: str, target_code: str) -> str:
    """Get language direction for UI layout."""
    # All supported languages (English, Japanese, Vietnamese) are LTR
    return 'ltr'


def validate_language_pair(source: str, target: str, service: str = 'google') -> bool:
    """Validate if a language pair is supported by the service."""
    if service.lower() == 'google':
        return (is_supported_by_google(source) and is_supported_by_google(target))
    elif service.lower() == 'deepl':
        return (is_supported_by_deepl(source) and is_supported_by_deepl(target))
    else:
        return True
