"""
Main Window Controller for handling business logic operations.
"""

import os
from ..services.language_detection_service import LanguageDetectionService


class MainWindowController:
    """Controller for handling main window business logic."""
    
    def __init__(self, main_window):
        """Initialize the controller with reference to main window."""
        self.main_window = main_window
    
    def handle_file_upload(self, file_path):
        """Handle file upload with business logic."""
        if not self.main_window.excel_handler:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error("Excel handler not initialized")
            return

        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info(f"Loading file: {file_path}")
            
            # Use actual ExcelHandler to load and analyze the file
            if os.path.exists(file_path) and file_path.lower().endswith(('.xlsx', '.xls')):
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_file_operation("load", os.path.basename(file_path), True)
                
                # Store loaded file information
                self.main_window.loaded_file_path = file_path
                
                # Clear existing sheets
                if hasattr(self.main_window, 'sheets_scroll'):
                    self.main_window.sheets_scroll.clear_sheets()
                
                # Analyze file with openpyxl
                self._analyze_excel_file(file_path)
                
            else:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error("Invalid file format. Please select an Excel file (.xlsx or .xls)")
                    
        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Failed to load file: {str(e)}")
    
    def _analyze_excel_file(self, file_path):
        """Analyze Excel file and extract information."""
        try:
            # Try to use openpyxl for basic file analysis
            import openpyxl
            workbook = openpyxl.load_workbook(file_path, read_only=True, data_only=True)
            
            # Get real sheet information
            self.main_window.loaded_sheets = []
            total_cells = 0

            # Extract sample texts for language detection before processing sheets
            sample_texts = LanguageDetectionService.extract_sample_texts(workbook)

            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Count cells with data (not empty)
                cell_count = 0
                for row in worksheet.iter_rows():
                    for cell in row:
                        if cell.value is not None and str(cell.value).strip():
                            cell_count += 1
                
                # Only add sheets that have data
                if cell_count > 0:
                    self.main_window.loaded_sheets.append({
                        "name": sheet_name,
                        "cells": cell_count
                    })
                    total_cells += cell_count
            
            workbook.close()

            # Update total cell count
            self.main_window.loaded_file_cells = total_cells

            # Detect language from extracted sample texts (after workbook is closed)
            self._detect_file_language_from_samples(sample_texts)
            
            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== FILE LOADED SUCCESSFULLY ======")
                self.main_window.business_logger.log_info(f"Detected {len(self.main_window.loaded_sheets)} sheets with data")
                self.main_window.business_logger.log_info(f"Total translatable cells: {self.main_window.loaded_file_cells}")
            
            # Populate sheets scroll area with real data
            if hasattr(self.main_window, 'sheets_scroll'):
                for sheet in self.main_window.loaded_sheets:
                    self.main_window.sheets_scroll.add_sheet(sheet["name"], sheet["cells"], True)
            
            # Calculate and suggest optimal batch size
            optimal_batch_size = LanguageDetectionService.calculate_optimal_batch_size(self.main_window.loaded_file_cells)
            if hasattr(self.main_window, 'batch_size_input'):
                self.main_window.batch_size_input.setValue(optimal_batch_size)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Suggested batch size: {optimal_batch_size}")
                    
        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error analyzing Excel file: {str(e)}")
    
    def _detect_file_language_from_samples(self, sample_texts):
        """Detect language from pre-extracted sample texts."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info("Detecting language from file content...")

            if sample_texts:
                # Use the language detection service
                detected_language = LanguageDetectionService.detect_language_from_samples(sample_texts)
                
                if detected_language:
                    # Convert language code to readable name
                    language_name = LanguageDetectionService.get_language_name(detected_language)
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_info(f"Detected language: {language_name} ({detected_language}) using Heuristic Detection")
                else:
                    if self.main_window.business_logger:
                        self.main_window.business_logger.log_warning("Could not detect language from file content")
            else:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_warning("No text content found for language detection")

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Error during language detection: {str(e)}")
    
    def handle_export(self):
        """Handle file export."""
        try:
            # Check if translated workbook is available
            if not hasattr(self.main_window, 'translated_workbook') or not self.main_window.translated_workbook:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error("Export failed: No translated workbook available")
                    self.main_window.business_logger.log_error("Please complete a successful translation before exporting")

                # Show error dialog
                from PyQt6.QtWidgets import QMessageBox
                from gui.styles.dialog_style import MESSAGE_BOX_STYLE

                msg_box = QMessageBox(self.main_window)
                msg_box.setWindowTitle("Export Failed")
                msg_box.setText("No translated workbook available for export.\n\nPlease complete a successful translation before attempting to export.")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
                msg_box.exec()

                return

            # Show file save dialog
            from PyQt6.QtWidgets import QFileDialog
            from gui.styles.dialog_style import FILE_DIALOG_STYLE

            dialog = QFileDialog(self.main_window)
            dialog.setWindowTitle("Export Translated File")
            dialog.setNameFilter("Excel Files (*.xlsx);;All Files (*)")
            dialog.setFileMode(QFileDialog.FileMode.AnyFile)
            dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
            dialog.setViewMode(QFileDialog.ViewMode.Detail)
            dialog.setDefaultSuffix("xlsx")

            # Set default filename
            if self.main_window.loaded_file_path:
                from pathlib import Path
                original_path = Path(self.main_window.loaded_file_path)
                default_name = f"{original_path.stem}_translated{original_path.suffix}"
                dialog.selectFile(default_name)

            # Apply styling
            dialog.setStyleSheet(FILE_DIALOG_STYLE)

            if dialog.exec() == QFileDialog.DialogCode.Accepted:
                file_paths = dialog.selectedFiles()
                if file_paths:
                    output_path = Path(file_paths[0])
                    # Use Excel export method
                    self._export_excel_translated_file(output_path)

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Export failed: {str(e)}")



    def _get_or_create_event_loop(self):
        """Get existing event loop or create a new one safely."""
        import asyncio
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
            return loop
        except RuntimeError:
            # Create a new event loop if none exists or current one is closed
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info("Created new event loop for export")
                return loop
            except Exception as e:
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_error(f"Failed to create event loop: {e}")
                raise
    
    def handle_source_language_change(self, language):
        """Handle source language change."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info(f"Source language changed to: {language}")

    def handle_target_language_change(self, language):
        """Handle target language change."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info(f"Target language changed to: {language}")
    
    def handle_translator_change(self, translator):
        """Handle translator service change."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info(f"Translator changed to: {translator}")

    def handle_batch_size_change(self, old_value, new_value):
        """Handle batch size input changes with logging."""
        if self.main_window.business_logger:
            self.main_window.business_logger.log_info(f"Batch size changed from {old_value} to {new_value}")

    def _export_excel_translated_file(self, output_path):
        """Export the translated file using the Excel translation logic."""
        try:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_info(f"Starting Excel export to: {output_path}")

            # Check if we have a translated workbook
            if not hasattr(self.main_window, 'translated_workbook') or not self.main_window.translated_workbook:
                raise Exception("No translated workbook available")

            # Create progress callback function
            def progress_callback(progress: int, message: str):
                if hasattr(self.main_window, 'progress_bar'):
                    self.main_window.progress_bar.set_progress(progress, message)
                if self.main_window.business_logger:
                    self.main_window.business_logger.log_info(f"Export Progress: {progress}% - {message}")

            # Export the file directly using openpyxl
            progress_callback(95, f"Saving file to {output_path}...")
            self.main_window.translated_workbook.save(output_path)
            progress_callback(100, "File exported successfully!")

            if self.main_window.business_logger:
                self.main_window.business_logger.log_status_message("====== FILE EXPORTED SUCCESSFULLY ======")
                self.main_window.business_logger.log_info(f"Successfully exported translated file to: {output_path}")

            # Update progress bar with export success status
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(100, "File exported successfully")

            # Show success message
            from PyQt6.QtWidgets import QMessageBox
            from gui.styles.dialog_style import MESSAGE_BOX_STYLE

            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("Export Successful")
            msg_box.setText(f"Translated file has been successfully exported to:\n{output_path}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

        except Exception as e:
            if self.main_window.business_logger:
                self.main_window.business_logger.log_error(f"Excel export failed: {str(e)}")

            # Show error message
            from PyQt6.QtWidgets import QMessageBox
            from gui.styles.dialog_style import MESSAGE_BOX_STYLE

            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("Export Failed")
            msg_box.setText(f"Failed to export translated file: {str(e)}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

            # Update progress bar
            if hasattr(self.main_window, 'progress_bar'):
                self.main_window.progress_bar.set_progress(0, "Export failed")
