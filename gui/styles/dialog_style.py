"""
Dialog and Window Component Styling

Styles for dialogs, status bars, and other window-level components.
"""

# Settings Dialog Styles
SETTINGS_DIALOG_STYLE = """
QDialog {
    background-color: #2b2b2b;
    color: #ffffff;
}

QLabel#settingsTitle {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
}

QLabel#settingsContent {
    font-size: 12px;
    color: #cccccc;
}

QPushButton#settingsButton {
    background-color: #3c3c3c;
    border: 1px solid #00aaff;
    border-radius: 6px;
    color: #ffffff;
    font-size: 12px;
    padding: 8px 16px;
    min-width: 80px;
}

QPushButton#settingsButton:hover {
    background-color: #4a4a4a;
    border-color: #00ccff;
}

QPushButton#settingsButton:pressed {
    background-color: #2a2a2a;
}
"""

# Status Bar Styles
STATUS_BAR_STYLE = """
QStatusBar {
    background-color: #0d1117;
    color: #8b949e;
    border-top: 1px solid #30363d;
    font-size: 11px;
    padding: 4px 8px;
}

QStatusBar QLabel {
    color: #8b949e;
    font-size: 11px;
    padding: 4px 8px;
    background-color: transparent;
}
"""

# General Dialog Style
DIALOG_STYLE = """
QDialog {
    background-color: #0d1117;
    color: #f0f6fc;
}
"""

# Message Box Styles (Midnight Dark Theme)
MESSAGE_BOX_STYLE = """
QMessageBox {
    background-color: #0d1117;
    color: #f0f6fc;
    font-size: 13px;
    border: 1px solid #30363d;
    border-radius: 8px;
}

QMessageBox QLabel {
    background-color: transparent;
    color: #f0f6fc;
    font-size: 13px;
    padding: 8px;
}

QMessageBox QPushButton {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 12px;
    font-weight: bold;
    padding: 8px 16px;
    min-width: 80px;
    margin: 4px;
}

QMessageBox QPushButton:hover {
    background-color: #30363d;
    border-color: #484f58;
}

QMessageBox QPushButton:pressed {
    background-color: #161b22;
    border-color: #21262d;
}

QMessageBox QPushButton:default {
    background-color: #238636;
    border-color: #2ea043;
}

QMessageBox QPushButton:default:hover {
    background-color: #2ea043;
    border-color: #3fb950;
}

QMessageBox QPushButton:default:pressed {
    background-color: #1a7f37;
    border-color: #238636;
}

QMessageBox QIcon {
    background-color: transparent;
}
"""

# File Dialog Styles (Midnight Dark Theme)
FILE_DIALOG_STYLE = """
QFileDialog {
    background-color: #0d1117;
    color: #f0f6fc;
    font-size: 13px;
}

QFileDialog QLabel {
    background-color: transparent;
    color: #f0f6fc;
}

QFileDialog QLineEdit {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    padding: 6px 8px;
    font-size: 13px;
}

QFileDialog QLineEdit:focus {
    border-color: #58a6ff;
    background-color: #0d1117;
}

QFileDialog QPushButton {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 12px;
    padding: 8px 16px;
    min-width: 80px;
}

QFileDialog QPushButton:hover {
    background-color: #30363d;
    border-color: #484f58;
}

QFileDialog QPushButton:pressed {
    background-color: #161b22;
    border-color: #21262d;
}

QFileDialog QListView {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    selection-background-color: #30363d;
    selection-color: #f0f6fc;
    outline: none;
}

QFileDialog QTreeView {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    selection-background-color: #30363d;
    selection-color: #f0f6fc;
    outline: none;
}

QFileDialog QComboBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    padding: 6px 8px;
    font-size: 13px;
}

QFileDialog QComboBox:hover {
    border-color: #484f58;
}

QFileDialog QComboBox::drop-down {
    border: none;
    background-color: transparent;
}

QFileDialog QComboBox::down-arrow {
    image: url(gui/assets/icons/dropdown_arrow.png);
    width: 12px;
    height: 12px;
}
"""
