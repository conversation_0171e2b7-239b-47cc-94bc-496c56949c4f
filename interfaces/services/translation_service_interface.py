"""
Translation service interface for translation operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from domain.entities.translation import TranslationRequest, TranslationResult, BatchTranslation


class TranslationServiceInterface(ABC):
    """Interface for translation service operations."""
    
    @abstractmethod
    async def translate_text(self, request: TranslationRequest) -> TranslationResult:
        """Translate a single text."""
        ...
    
    @abstractmethod
    async def translate_batch(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Translate a batch of texts."""
        ...
    
    @abstractmethod
    async def detect_language(self, text: str) -> Optional[str]:
        """Detect the language of the text."""
        ...
    
    @abstractmethod
    async def get_supported_languages(self) -> Dict[str, str]:
        """Get supported languages."""
        ...
    
    @abstractmethod
    async def validate_api_key(self) -> bool:
        """Validate the API key."""
        ...
    
    @abstractmethod
    def get_service_name(self) -> str:
        """Get the service name."""
        ...
    
    @abstractmethod
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information."""
        ...
    
    @abstractmethod
    async def check_service_health(self) -> bool:
        """Check if the service is available."""
        ...
