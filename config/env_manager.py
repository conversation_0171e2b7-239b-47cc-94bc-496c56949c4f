"""
Environment Configuration Manager

This module handles reading from and writing to the .env file for application configuration.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional


class EnvManager:
    """Manager for .env file operations."""
    
    def __init__(self, env_file_path: str = ".env"):
        """Initialize the environment manager.
        
        Args:
            env_file_path: Path to the .env file
        """
        self.env_file_path = Path(env_file_path)
        self._ensure_env_file_exists()
    
    def _ensure_env_file_exists(self):
        """Ensure the .env file exists with default values."""
        if not self.env_file_path.exists():
            self._create_default_env_file()
    
    def _create_default_env_file(self):
        """Create a default .env file with standard configuration."""
        default_content = """# Excel Translator Configuration
# Maximum file size in megabytes
MAX_FILE_SIZE_MB=50

# Supported file formats (comma-separated, without dots)
SUPPORTED_FORMATS=xlsx,xls

# API Keys (leave empty if not using)
GOOGLE_API_KEY=
DEEPL_API_KEY=

# UI Settings
DEFAULT_LANGUAGE=en
"""
        
        try:
            with open(self.env_file_path, 'w', encoding='utf-8') as f:
                f.write(default_content)
        except Exception as e:
            print(f"Warning: Could not create .env file: {e}")
    
    def read_env_file(self) -> Dict[str, str]:
        """Read all environment variables from the .env file.
        
        Returns:
            Dictionary of environment variable key-value pairs
        """
        env_vars = {}
        
        if not self.env_file_path.exists():
            return env_vars
        
        try:
            with open(self.env_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse key=value pairs
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        env_vars[key] = value
                    else:
                        print(f"Warning: Invalid line {line_num} in .env file: {line}")
        
        except Exception as e:
            print(f"Error reading .env file: {e}")
        
        return env_vars
    
    def write_env_file(self, env_vars: Dict[str, str]):
        """Write environment variables to the .env file.
        
        Args:
            env_vars: Dictionary of environment variable key-value pairs
        """
        try:
            # Read existing file to preserve comments and structure
            existing_lines = []
            if self.env_file_path.exists():
                with open(self.env_file_path, 'r', encoding='utf-8') as f:
                    existing_lines = f.readlines()
            
            # Process lines and update values
            updated_lines = []
            updated_keys = set()
            
            for line in existing_lines:
                stripped_line = line.strip()
                
                # Keep comments and empty lines as-is
                if not stripped_line or stripped_line.startswith('#'):
                    updated_lines.append(line)
                    continue
                
                # Update existing key-value pairs
                if '=' in stripped_line:
                    key = stripped_line.split('=', 1)[0].strip()
                    if key in env_vars:
                        updated_lines.append(f"{key}={env_vars[key]}\n")
                        updated_keys.add(key)
                    else:
                        updated_lines.append(line)
                else:
                    updated_lines.append(line)
            
            # Add new keys that weren't in the original file
            for key, value in env_vars.items():
                if key not in updated_keys:
                    updated_lines.append(f"{key}={value}\n")
            
            # Write the updated content
            with open(self.env_file_path, 'w', encoding='utf-8') as f:
                f.writelines(updated_lines)
        
        except Exception as e:
            print(f"Error writing .env file: {e}")
    
    def get_value(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get a specific environment variable value.
        
        Args:
            key: Environment variable key
            default: Default value if key not found
            
        Returns:
            Environment variable value or default
        """
        env_vars = self.read_env_file()
        return env_vars.get(key, default)
    
    def set_value(self, key: str, value: str):
        """Set a specific environment variable value.
        
        Args:
            key: Environment variable key
            value: Environment variable value
        """
        env_vars = self.read_env_file()
        env_vars[key] = value
        self.write_env_file(env_vars)
    
    def get_max_file_size_mb(self) -> float:
        """Get the maximum file size in MB.
        
        Returns:
            Maximum file size in MB
        """
        value = self.get_value('MAX_FILE_SIZE_MB', '50')
        try:
            return float(value)
        except ValueError:
            return 50.0
    
    def set_max_file_size_mb(self, size_mb: float):
        """Set the maximum file size in MB.
        
        Args:
            size_mb: Maximum file size in MB
        """
        self.set_value('MAX_FILE_SIZE_MB', str(int(size_mb)))
    
    def get_supported_formats(self) -> List[str]:
        """Get the list of supported file formats.
        
        Returns:
            List of supported file formats (with dots)
        """
        value = self.get_value('SUPPORTED_FORMATS', 'xlsx,xls')
        formats = [fmt.strip() for fmt in value.split(',') if fmt.strip()]
        # Ensure formats have dots
        return [f'.{fmt.lstrip(".")}' for fmt in formats]
    
    def set_supported_formats(self, formats: List[str]):
        """Set the list of supported file formats.
        
        Args:
            formats: List of supported file formats (with or without dots)
        """
        # Remove dots for storage in .env file
        clean_formats = [fmt.lstrip('.') for fmt in formats]
        self.set_value('SUPPORTED_FORMATS', ','.join(clean_formats))
    
    def update_file_settings(self, max_file_size_mb: float, supported_formats: List[str]):
        """Update both file size and supported formats in one operation.
        
        Args:
            max_file_size_mb: Maximum file size in MB
            supported_formats: List of supported file formats
        """
        env_vars = self.read_env_file()
        env_vars['MAX_FILE_SIZE_MB'] = str(int(max_file_size_mb))
        
        # Remove dots for storage
        clean_formats = [fmt.lstrip('.') for fmt in supported_formats]
        env_vars['SUPPORTED_FORMATS'] = ','.join(clean_formats)
        
        self.write_env_file(env_vars)


# Global instance
_env_manager = None


def get_env_manager() -> EnvManager:
    """Get the global environment manager instance.
    
    Returns:
        Global EnvManager instance
    """
    global _env_manager
    if _env_manager is None:
        _env_manager = EnvManager()
    return _env_manager
