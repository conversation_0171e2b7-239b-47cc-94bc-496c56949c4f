from PyQt6.QtWidgets import Q<PERSON><PERSON>rollArea, QWidget, QVBoxLayout, QCheckBox
from PyQt6.QtCore import Qt
from gui.components.check_box.sheet_checkbox import Sheet<PERSON>heckB<PERSON>
from ...styles.sheets_scroll_area_style import SHEETS_SCROLL_AREA_STYLE
from ...managers.localization_manager import get_localization_manager, tr


class SheetsScrollArea(QScrollArea):
    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self.sheet_data = []  # Store sheet data for language updates
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_texts)

    def setup_ui(self):
        self.setFixedHeight(125)
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Create content widget
        self.content_widget = QWidget()
        self.content_widget.setObjectName("sheetsContent")
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(15, 12, 15, 12)
        self.content_layout.setSpacing(4)
        self.content_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.setWidget(self.content_widget)
    
    def apply_styles(self):
        self.setStyleSheet(SHEETS_SCROLL_AREA_STYLE)
    
    def add_sheet(self, sheet_name, cell_count, checked=True):
        """Add a new sheet checkbox"""
        # Store sheet data for language updates
        sheet_data = {
            'name': sheet_name,
            'cell_count': cell_count,
            'checked': checked
        }
        self.sheet_data.append(sheet_data)

        text = f"{sheet_name}: {cell_count} {tr('translatable_cells')}"
        checkbox = SheetCheckBox(text)
        checkbox.setChecked(checked)
        self.content_layout.addWidget(checkbox)
        return checkbox
    
    def clear_sheets(self):
        """Remove all sheet checkboxes"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        self.sheet_data.clear()  # Clear stored data
    
    def get_selected_sheets(self):
        """Get list of selected sheet names (without cell count info)"""
        selected = []
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isChecked():
                # Extract just the sheet name from the checkbox text
                # Format is "SheetName: X translatable_cells"
                # Need to find the last occurrence of ": X translatable_cells" pattern
                checkbox_text = widget.text()
                # Look for the pattern ": number translatable_cells" at the end
                import re
                match = re.search(r': \d+ .+$', checkbox_text)
                if match:
                    # Remove the cell count part
                    sheet_name = checkbox_text[:match.start()].strip()
                else:
                    # Fallback to original logic if pattern not found
                    sheet_name = checkbox_text.split(':')[0].strip()
                selected.append(sheet_name)
        return selected

    def get_all_sheet_names(self):
        """Get list of all sheet names (regardless of selection)"""
        all_sheets = []
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox):
                # Extract just the sheet name from the checkbox text
                # Format is "SheetName: X translatable_cells"
                # Need to find the last occurrence of ": X translatable_cells" pattern
                checkbox_text = widget.text()
                # Look for the pattern ": number translatable_cells" at the end
                import re
                match = re.search(r': \d+ .+$', checkbox_text)
                if match:
                    # Remove the cell count part
                    sheet_name = checkbox_text[:match.start()].strip()
                else:
                    # Fallback to original logic if pattern not found
                    sheet_name = checkbox_text.split(':')[0].strip()
                all_sheets.append(sheet_name)
        return all_sheets

    def update_texts(self):
        """Update all sheet checkbox texts when language changes."""
        # Clear current checkboxes
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Recreate checkboxes with updated text
        for sheet_data in self.sheet_data:
            text = f"{sheet_data['name']}: {sheet_data['cell_count']} {tr('translatable_cells')}"
            checkbox = SheetCheckBox(text)
            checkbox.setChecked(sheet_data['checked'])
            self.content_layout.addWidget(checkbox)
