

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QProgress<PERSON>ar
from ...styles.styled_progress_bar_style import STYLED_PROGRESS_BAR_STYLE


class StyledProgressBar(QProgressBar):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        self.setMinimum(0)
        self.setMaximum(100)
        self.setValue(0)
        self.setFixedHeight(20)
        self.setTextVisible(False)

    def apply_styles(self):
        self.setStyleSheet(STYLED_PROGRESS_BAR_STYLE)
