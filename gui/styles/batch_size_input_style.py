BATCH_SIZE_INPUT_STYLE = """
QSpinBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 10px 40px 10px 15px;
    color: #f0f6fc;
    font-size: 13px;
    min-width: 120px;
    min-height: 20px;
}

QSpinBox:hover {
    border-color: #484f58;
    background-color: #30363d;
}

QSpinBox:focus {
    border-color: #666666;
    outline: none;
}

QSpinBox::up-button {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    height: 12px;
    border: none;
    border-radius: 10px;
    background-color: #30363d;
    margin-right: 10px;
    margin-top: 4px;
    margin-bottom: 1px;
}

QSpinBox::up-button:hover {
    background-color: #484f58;
}

QSpinBox::up-button:pressed {
    background-color: #21262d;
}

QSpinBox::up-arrow {
    image: url(gui/assets/icons/spinbox_up_arrow.png);
    width: 10px;
    height: 6px;
    margin: 0px;
    background: transparent;
    border: none;
}

QSpinBox::down-button {
    subcontrol-origin: padding;
    subcontrol-position: bottom right;
    width: 20px;
    height: 12px;
    border: none;
    border-radius: 10px;
    background-color: #30363d;
    margin-right: 10px;
    margin-top: 1px;
    margin-bottom: 4px;
}

QSpinBox::down-button:hover {
    background-color: #666666;
}

QSpinBox::down-button:pressed {
    background-color: #444444;
}

QSpinBox::down-arrow {
    image: url(gui/assets/icons/spinbox_down_arrow.png);
    width: 10px;
    height: 6px;
    margin: 0px;
    background: transparent;
    border: none;
}
"""
