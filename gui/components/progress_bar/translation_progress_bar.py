from PyQt6.QtWidgets import <PERSON><PERSON>idget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import <PERSON>Font, QPainter, QColor
import time
from ...styles.translation_progress_bar_style import TRANSLATION_PROGRESS_BAR_STYLE


class EnhancedProgressBar(QProgressBar):
    """Enhanced progress bar with smooth animations and visual states."""

    def __init__(self):
        super().__init__()
        self._animation = QPropertyAnimation(self, b"value")
        self._animation.setDuration(300)  # 300ms smooth animation
        self._animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self._error_state = False
        self._warning_state = False

    def setValueAnimated(self, value):
        """Set value with smooth animation."""
        if abs(value - self.value()) > 5:  # Only animate for significant changes
            self._animation.setStartValue(self.value())
            self._animation.setEndValue(value)
            self._animation.start()
        else:
            # For small changes, set immediately
            self.setValue(value)

    def setErrorState(self, error=True):
        """Set error visual state."""
        self._error_state = error
        self.update()

    def setWarningState(self, warning=True):
        """Set warning visual state."""
        self._warning_state = warning
        self.update()

    def paintEvent(self, event):
        """Custom paint event for enhanced visuals."""
        super().paintEvent(event)

        # Add pulsing effect for active translation
        if self.value() > 0 and self.value() < 100 and not self._error_state:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Add subtle glow effect
            glow_color = QColor(0, 170, 255, 30)  # Semi-transparent blue
            painter.fillRect(self.rect(), glow_color)


class TranslationProgressBar(QWidget):
    def __init__(self):
        super().__init__()
        self._start_time = None
        self._last_update_time = None
        self._total_cells = 0
        self._processed_cells = 0
        self._current_speed = 0.0
        self._eta_seconds = 0
        self._current_phase = "Ready"
        self._is_cancelled = False
        self._error_count = 0
        self._retry_count = 0
        self._target_progress = 0  # Store target progress value

        # Internal tracking variables (not displayed in UI)
        self._speed_text = ""
        self._eta_text = ""
        self._cells_text = ""
        self._error_text = ""
        self._final_time = 0
        self._final_speed = 0

        self.setup_ui()
        self.apply_styles()

        # Timer for real-time updates
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._update_metrics)
        self._update_timer.start(1000)  # Update every second
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Progress info layout (keep original layout)
        info_layout = QHBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setObjectName("progressStatus")
        info_layout.addWidget(self.status_label)

        # Spacer
        info_layout.addStretch()

        # Percentage label
        self.percentage_label = QLabel("0%")
        self.percentage_label.setObjectName("progressPercentage")
        info_layout.addWidget(self.percentage_label)

        layout.addLayout(info_layout)

        # Progress bar (keep original appearance but use enhanced version)
        self.progress_bar = EnhancedProgressBar()
        self.progress_bar.setObjectName("translationProgress")
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)  # Start at 0, no mock progress
        self.progress_bar.setFixedHeight(22)  # Keep original height
        layout.addWidget(self.progress_bar)

        # Hidden metrics labels (for internal tracking, not displayed)
        self.speed_label = QLabel("")
        self.eta_label = QLabel("")
        self.cells_label = QLabel("")
        self.error_label = QLabel("")
        # Don't add these to layout - keep them hidden
    
    def apply_styles(self):
        self.setStyleSheet(TRANSLATION_PROGRESS_BAR_STYLE)

    def set_progress(self, value, status_text="Translating...", cells_processed=None, total_cells=None,
                    error_count=None, retry_count=None, phase=None):
        """Enhanced progress update with detailed metrics (keeping original UI)."""
        # Update basic progress (original interface)
        self._target_progress = value  # Store target value
        self.progress_bar.setValueAnimated(value)
        self.percentage_label.setText(f"{value}%")
        self.status_label.setText(status_text)

        # Update internal state for enhanced tracking
        if phase:
            self._current_phase = phase
        if cells_processed is not None:
            self._processed_cells = cells_processed
        if total_cells is not None:
            self._total_cells = total_cells
        if error_count is not None:
            self._error_count = error_count
        if retry_count is not None:
            self._retry_count = retry_count

        # Start timing if this is the first progress update
        if self._start_time is None and value > 0:
            self._start_time = time.time()

        # Update last update time
        self._last_update_time = time.time()

        # Calculate and update metrics (internal tracking)
        self._calculate_metrics()
        self._update_internal_metrics()

        # Set visual states (subtle changes only)
        if error_count and error_count > 0:
            self.progress_bar.setWarningState(True)
        else:
            self.progress_bar.setWarningState(False)

    def set_error_state(self, error_message="Translation failed"):
        """Set progress bar to error state."""
        self.progress_bar.setErrorState(True)
        self.status_label.setText(error_message)
        self.speed_label.setText("❌ Error")
        self.eta_label.setText("")
        self._is_cancelled = False

    def set_cancelled_state(self):
        """Set progress bar to cancelled state."""
        self._is_cancelled = True
        self.progress_bar.setWarningState(True)
        self.status_label.setText("Translation cancelled")
        self.speed_label.setText("⏹️ Cancelled")
        self.eta_label.setText("")

    def set_completed_state(self):
        """Set progress bar to completed state (keeping original UI)."""
        self._target_progress = 100
        self.progress_bar.setValue(100)  # Set immediately for completion
        self.percentage_label.setText("100%")
        self.status_label.setText("Translation completed successfully")
        self.progress_bar.setErrorState(False)
        self.progress_bar.setWarningState(False)

        # Calculate final statistics (internal tracking)
        if self._start_time:
            total_time = time.time() - self._start_time
            avg_speed = self._processed_cells / total_time if total_time > 0 else 0
            # Store metrics internally but don't display them in UI
            self._final_time = total_time
            self._final_speed = avg_speed

    def reset(self):
        """Reset progress bar to initial state."""
        self._start_time = None
        self._last_update_time = None
        self._total_cells = 0
        self._processed_cells = 0
        self._current_speed = 0.0
        self._eta_seconds = 0
        self._current_phase = "Ready"
        self._is_cancelled = False
        self._error_count = 0
        self._retry_count = 0
        self._target_progress = 0

        self.progress_bar.setValue(0)
        self.percentage_label.setText("0%")
        self.status_label.setText("Ready")
        self.speed_label.setText("")
        self.eta_label.setText("")
        self.cells_label.setText("")
        self.error_label.setText("")

        self.progress_bar.setErrorState(False)
        self.progress_bar.setWarningState(False)

    def _calculate_metrics(self):
        """Calculate speed and ETA metrics."""
        if not self._start_time or self._processed_cells == 0:
            return

        current_time = time.time()
        elapsed_time = current_time - self._start_time

        if elapsed_time > 0:
            # Calculate current speed (cells per second)
            self._current_speed = self._processed_cells / elapsed_time

            # Calculate ETA
            if self._total_cells > 0 and self._current_speed > 0:
                remaining_cells = self._total_cells - self._processed_cells
                self._eta_seconds = remaining_cells / self._current_speed

    def _update_internal_metrics(self):
        """Update internal metrics (not displayed in UI to keep original appearance)."""
        # Update speed tracking
        if self._current_speed > 0:
            if self._current_speed >= 1:
                self._speed_text = f"⚡ {self._current_speed:.1f} cells/sec"
            else:
                self._speed_text = f"⚡ {self._current_speed:.2f} cells/sec"
        else:
            self._speed_text = ""

        # Update ETA tracking
        if self._eta_seconds > 0 and not self._is_cancelled:
            self._eta_text = f"⏱️ ETA: {self._format_time(self._eta_seconds)}"
        else:
            self._eta_text = ""

        # Update cells processed tracking
        if self._total_cells > 0:
            self._cells_text = f"📊 {self._processed_cells}/{self._total_cells} cells"
        else:
            self._cells_text = ""

        # Update error/retry tracking
        error_parts = []
        if self._error_count > 0:
            error_parts.append(f"❌ {self._error_count} errors")
        if self._retry_count > 0:
            error_parts.append(f"🔄 {self._retry_count} retries")

        if error_parts:
            self._error_text = " | ".join(error_parts)
        else:
            self._error_text = ""

    def _update_metrics(self):
        """Timer callback to update metrics in real-time."""
        if self._start_time and not self._is_cancelled:
            self._calculate_metrics()
            self._update_internal_metrics()

    def _format_time(self, seconds):
        """Format time in a human-readable format."""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"

    def get_progress_stats(self):
        """Get current progress statistics."""
        return {
            'progress_percent': self._target_progress,  # Use target value instead of animated value
            'status': self.status_label.text(),
            'phase': self._current_phase,
            'processed_cells': self._processed_cells,
            'total_cells': self._total_cells,
            'current_speed': self._current_speed,
            'eta_seconds': self._eta_seconds,
            'error_count': self._error_count,
            'retry_count': self._retry_count,
            'elapsed_time': time.time() - self._start_time if self._start_time else 0,
            'is_cancelled': self._is_cancelled
        }

    def get_enhanced_metrics(self):
        """Get enhanced metrics for logging/debugging (not displayed in UI)."""
        return {
            'speed_text': self._speed_text,
            'eta_text': self._eta_text,
            'cells_text': self._cells_text,
            'error_text': self._error_text,
            'final_time': self._final_time,
            'final_speed': self._final_speed,
            'has_errors': self._error_count > 0,
            'has_retries': self._retry_count > 0
        }
