
from PyQt6.QtWidgets import <PERSON><PERSON>ush<PERSON>utton
from ...styles.cancel_button_style import CANCEL_BUTTON_STYLE
from ...managers.localization_manager import get_localization_manager, tr


class CancelButton(QPushButton):
    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_text)

    def setup_ui(self):
        self.setText(tr("cancel_button"))
        self.setFixedHeight(40)
        self.setMinimumWidth(100)

    def apply_styles(self):
        self.setStyleSheet(CANCEL_BUTTON_STYLE)

    def update_text(self):
        """Update button text when language changes."""
        self.setText(tr("cancel_button"))
