"""
Language Detection Service for Excel files.
"""


class LanguageDetectionService:
    """Service for detecting languages from Excel file content."""
    
    @staticmethod
    def extract_sample_texts(workbook):
        """Extract sample texts from Excel workbook for language detection."""
        sample_texts = []
        max_samples = 5  # Limit to first 5 text samples for detection

        try:
            for sheet_name in workbook.sheetnames:
                if len(sample_texts) >= max_samples:
                    break

                worksheet = workbook[sheet_name]

                # Iterate through cells to find text content
                for row in worksheet.iter_rows():
                    if len(sample_texts) >= max_samples:
                        break
                    for cell in row:
                        if len(sample_texts) >= max_samples:
                            break
                        if cell.value and isinstance(cell.value, str):
                            text = str(cell.value).strip()
                            # Check if it's meaningful text (not just numbers or symbols)
                            if len(text) > 3 and any(c.isalpha() for c in text):
                                sample_texts.append(text)
        except Exception as e:
            # Let the caller handle logging
            raise e

        return sample_texts
    
    @staticmethod
    def detect_language_from_samples(sample_texts):
        """Detect language from pre-extracted sample texts."""
        if not sample_texts:
            return None
            
        # Combine sample texts for detection
        combined_text = " ".join(sample_texts[:3])  # Use first 3 samples
        
        # Use heuristic detection to avoid threading issues
        return LanguageDetectionService.detect_language_heuristic(combined_text)
    
    @staticmethod
    def get_language_name(language_code):
        """Convert language code to readable name."""
        language_map = {
            'en': 'English',
            'ja': 'Japanese',
            'vi': 'Vietnamese',
            'zh': 'Chinese',
            'ko': 'Korean',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'th': 'Thai',
            'id': 'Indonesian',
            'ms': 'Malay',
            'tl': 'Filipino',
            'nl': 'Dutch',
            'sv': 'Swedish',
            'da': 'Danish',
            'no': 'Norwegian',
            'fi': 'Finnish',
            'pl': 'Polish',
            'cs': 'Czech',
            'sk': 'Slovak',
            'hu': 'Hungarian',
            'ro': 'Romanian',
            'bg': 'Bulgarian',
            'hr': 'Croatian',
            'sr': 'Serbian',
            'sl': 'Slovenian',
            'et': 'Estonian',
            'lv': 'Latvian',
            'lt': 'Lithuanian',
            'uk': 'Ukrainian',
            'be': 'Belarusian',
            'mk': 'Macedonian',
            'sq': 'Albanian',
            'mt': 'Maltese',
            'is': 'Icelandic',
            'ga': 'Irish',
            'cy': 'Welsh',
            'eu': 'Basque',
            'ca': 'Catalan',
            'gl': 'Galician',
            'tr': 'Turkish',
            'he': 'Hebrew',
            'fa': 'Persian',
            'ur': 'Urdu',
            'bn': 'Bengali',
            'ta': 'Tamil',
            'te': 'Telugu',
            'ml': 'Malayalam',
            'kn': 'Kannada',
            'gu': 'Gujarati',
            'pa': 'Punjabi',
            'mr': 'Marathi',
            'ne': 'Nepali',
            'si': 'Sinhala',
            'my': 'Myanmar',
            'km': 'Khmer',
            'lo': 'Lao',
            'ka': 'Georgian',
            'am': 'Amharic',
            'sw': 'Swahili',
            'zu': 'Zulu',
            'af': 'Afrikaans',
            'xh': 'Xhosa',
            'st': 'Sesotho',
            'tn': 'Setswana',
            'ss': 'Siswati',
            've': 'Venda',
            'ts': 'Tsonga',
            'nr': 'Ndebele'
        }
        return language_map.get(language_code.lower(), language_code.upper())
    
    @staticmethod
    def detect_language_heuristic(text):
        """Simple heuristic-based language detection for common languages."""
        if not text or len(text.strip()) < 3:
            return None

        text = text.lower()

        # Japanese detection - look for hiragana, katakana, or kanji patterns
        japanese_chars = ['あ', 'い', 'う', 'え', 'お', 'か', 'き', 'く', 'け', 'こ', 'が', 'ぎ', 'ぐ', 'げ', 'ご',
                         'さ', 'し', 'す', 'せ', 'そ', 'ざ', 'じ', 'ず', 'ぜ', 'ぞ', 'た', 'ち', 'つ', 'て', 'と',
                         'だ', 'ぢ', 'づ', 'で', 'ど', 'な', 'に', 'ぬ', 'ね', 'の', 'は', 'ひ', 'ふ', 'へ', 'ほ',
                         'ば', 'び', 'ぶ', 'べ', 'ぼ', 'ぱ', 'ぴ', 'ぷ', 'ぺ', 'ぽ', 'ま', 'み', 'む', 'め', 'も',
                         'や', 'ゆ', 'よ', 'ら', 'り', 'る', 'れ', 'ろ', 'わ', 'ゐ', 'ゑ', 'を', 'ん',
                         'ア', 'イ', 'ウ', 'エ', 'オ', 'カ', 'キ', 'ク', 'ケ', 'コ', 'ガ', 'ギ', 'グ', 'ゲ', 'ゴ',
                         'サ', 'シ', 'ス', 'セ', 'ソ', 'ザ', 'ジ', 'ズ', 'ゼ', 'ゾ', 'タ', 'チ', 'ツ', 'テ', 'ト',
                         'ダ', 'ヂ', 'ヅ', 'デ', 'ド', 'ナ', 'ニ', 'ヌ', 'ネ', 'ノ', 'ハ', 'ヒ', 'フ', 'ヘ', 'ホ',
                         'バ', 'ビ', 'ブ', 'ベ', 'ボ', 'パ', 'ピ', 'プ', 'ペ', 'ポ', 'マ', 'ミ', 'ム', 'メ', 'モ',
                         'ヤ', 'ユ', 'ヨ', 'ラ', 'リ', 'ル', 'レ', 'ロ', 'ワ', 'ヰ', 'ヱ', 'ヲ', 'ン']

        if any(char in text for char in japanese_chars):
            return 'ja'

        # Vietnamese detection - look for Vietnamese diacritics
        vietnamese_chars = ['ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'à', 'á', 'ả', 'ã', 'ạ', 'ằ', 'ắ', 'ẳ', 'ẵ', 'ặ',
                           'ầ', 'ấ', 'ẩ', 'ẫ', 'ậ', 'è', 'é', 'ẻ', 'ẽ', 'ẹ', 'ề', 'ế', 'ể', 'ễ', 'ệ', 'ì', 'í',
                           'ỉ', 'ĩ', 'ị', 'ò', 'ó', 'ỏ', 'õ', 'ọ', 'ồ', 'ố', 'ổ', 'ỗ', 'ộ', 'ờ', 'ớ', 'ở', 'ỡ',
                           'ợ', 'ù', 'ú', 'ủ', 'ũ', 'ụ', 'ừ', 'ứ', 'ử', 'ữ', 'ự', 'ỳ', 'ý', 'ỷ', 'ỹ', 'ỵ']

        if any(char in text for char in vietnamese_chars):
            return 'vi'

        # Chinese detection - look for common Chinese characters
        chinese_chars = ['的', '一', '是', '在', '不', '了', '有', '和', '人', '这', '中', '大', '为', '上', '个',
                        '国', '我', '以', '要', '他', '时', '来', '用', '们', '生', '到', '作', '地', '于', '出',
                        '就', '分', '对', '成', '会', '可', '主', '发', '年', '动', '同', '工', '也', '能', '下',
                        '过', '子', '说', '产', '种', '面', '而', '方', '后', '多', '定', '行', '学', '法', '所']

        if any(char in text for char in chinese_chars):
            return 'zh'

        # Korean detection - look for Hangul characters
        korean_chars = ['가', '나', '다', '라', '마', '바', '사', '아', '자', '차', '카', '타', '파', '하',
                       '각', '간', '갈', '감', '갑', '강', '개', '객', '거', '건', '걸', '검', '게', '겨',
                       '격', '견', '결', '경', '계', '고', '곡', '곤', '골', '공', '과', '관', '광', '교',
                       '구', '국', '군', '굴', '권', '귀', '규', '그', '극', '근', '글', '금', '급', '기']

        if any(char in text for char in korean_chars):
            return 'ko'

        # English detection - look for common English words and patterns
        english_words = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one',
                        'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see',
                        'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'name',
                        'product', 'description', 'category', 'price', 'item', 'code', 'number', 'total', 'amount']

        words = text.split()
        english_count = sum(1 for word in words if word in english_words)

        if english_count > 0 and len(words) > 0:
            if english_count / len(words) > 0.3:  # If 30% or more words are English
                return 'en'

        # Spanish detection - look for common Spanish words
        spanish_words = ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da',
                        'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'pero', 'todo',
                        'esta', 'como', 'muy', 'sin', 'sobre', 'más', 'me', 'hasta', 'donde', 'quien', 'desde']

        spanish_count = sum(1 for word in words if word in spanish_words)
        if spanish_count > 0 and len(words) > 0:
            if spanish_count / len(words) > 0.3:
                return 'es'

        # French detection - look for common French words
        french_words = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans',
                       'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand',
                       'en', 'me', 'même', 'elle', 'vous', 'ou', 'du', 'au', 'nous', 'comme', 'mais', 'pouvoir']

        french_count = sum(1 for word in words if word in french_words)
        if french_count > 0 and len(words) > 0:
            if french_count / len(words) > 0.3:
                return 'fr'

        # Default to English if no specific language detected but contains Latin characters
        if any(c.isalpha() for c in text):
            return 'en'

        return None
    
    @staticmethod
    def calculate_optimal_batch_size(total_cells):
        """Calculate optimal batch size based on file size and API limits."""
        if total_cells <= 50:
            return min(10, total_cells)
        elif total_cells <= 500:
            return min(25, total_cells)
        elif total_cells <= 2000:
            return min(50, total_cells)
        elif total_cells <= 5000:
            return min(100, total_cells)
        else:
            return min(200, total_cells)  # Large files get bigger batches
