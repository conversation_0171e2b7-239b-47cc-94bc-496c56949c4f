"""
Status Display Component for Excel Translator Application.

Displays visual status indicators with "======" separators to show current workflow stage.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from ...styles.status_display_style import STATUS_DISPLAY_STYLE


class StatusDisplay(QWidget):
    """Component for displaying workflow status messages with visual separators."""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()
        self.current_timer = None
    
    def setup_ui(self):
        """Setup the status display UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(0)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setObjectName("statusMessage")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)
        
        # Set font
        font = QFont()
        font.setPointSize(11)
        font.setBold(True)
        self.status_label.setFont(font)
        
        layout.addWidget(self.status_label)
    
    def apply_styles(self):
        """Apply styles to the status display."""
        self.setStyleSheet(STATUS_DISPLAY_STYLE)
    
    def show_welcome_status(self):
        """Show welcome status message."""
        self.show_status("====== WELCOME TO EXCEL TRANSLATOR ======", 0)
    
    def show_file_loaded_status(self, filename):
        """Show file loaded status message."""
        self.show_status(f"====== FILE LOADED: {filename} ======", 3000)
    
    def show_translation_started_status(self):
        """Show translation started status message."""
        self.show_status("====== TRANSLATION STARTED ======", 0)
    
    def show_translation_completed_status(self):
        """Show translation completed status message."""
        self.show_status("====== TRANSLATION COMPLETED ======", 5000)
    
    def show_translation_cancelled_status(self):
        """Show translation cancelled status message."""
        self.show_status("====== TRANSLATION CANCELLED ======", 3000)
    
    def show_file_exported_status(self):
        """Show file exported status message."""
        self.show_status("====== FILE EXPORTED SUCCESSFULLY ======", 5000)
    
    def show_status(self, message, auto_clear_ms=0):
        """
        Show a status message.
        
        Args:
            message: The status message to display
            auto_clear_ms: Time in milliseconds to auto-clear (0 = no auto-clear)
        """
        self.status_label.setText(message)
        
        # Clear any existing timer
        if self.current_timer:
            self.current_timer.stop()
            self.current_timer = None
        
        # Set auto-clear timer if specified
        if auto_clear_ms > 0:
            self.current_timer = QTimer()
            self.current_timer.timeout.connect(self.clear_status)
            self.current_timer.setSingleShot(True)
            self.current_timer.start(auto_clear_ms)
    
    def clear_status(self):
        """Clear the status message."""
        self.status_label.setText("Ready")
        if self.current_timer:
            self.current_timer.stop()
            self.current_timer = None
    
    def set_ready_status(self):
        """Set status to ready."""
        self.clear_status()
