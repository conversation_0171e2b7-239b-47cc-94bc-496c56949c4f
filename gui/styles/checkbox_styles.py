"""
Checkbox styling for the Translation Application
"""

CHECKBOX_STYLE = """
QCheckBox {
    color: #f0f6fc;
    font-size: 13px;
    spacing: 8px;
    background-color: transparent;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #30363d;
    border-radius: 3px;
    background-color: #0d1117;
}

QCheckBox::indicator:hover {
    border-color: #484f58;
}

QCheckBox::indicator:checked {
    background-color: #30363d;
    border-color: #30363d;
    image: none;
}

QCheckBox::indicator:checked:hover {
    background-color: #484f58;
    border-color: #484f58;
}

QCheckBox::indicator:unchecked {
    background-color: #0d1117;
    border-color: #30363d;
}

QCheckBox::indicator:unchecked:hover {
    border-color: #484f58;
    background-color: #161b22;
}

QCheckBox:disabled {
    color: #484f58;
}

QCheckBox::indicator:disabled {
    background-color: #0d1117;
    border-color: #21262d;
}
"""

SHEET_CHECKBOX_STYLE = CHECKBOX_STYLE
