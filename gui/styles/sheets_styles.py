"""
Sheets Detection Section styling for the Translation Application
"""

SHEETS_SCROLL_AREA_STYLE = """
QScrollArea {
    background-color: #1a1a1a;
    border: 1px solid #00aaff;
    border-radius: 8px;
    padding: 2px;
}

QWidget#sheetsContent {
    background-color: #1a1a1a;
    border-radius: 6px;
    margin: 0px;
}

QCheckBox {
    color: #ffffff;
    font-size: 13px;
    spacing: 8px;
    background-color: transparent;
    padding: 6px 4px;
    border-radius: 4px;
}

QCheckBox:hover {
    background-color: #252525;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #555555;
    border-radius: 3px;
    background-color: #2b2b2b;
}

QCheckBox::indicator:hover {
    border-color: #00aaff;
    background-color: #333333;
}

QCheckBox::indicator:checked {
    background-color: #00aaff;
    border-color: #00aaff;
    image: none;
}

QCheckBox::indicator:checked:hover {
    background-color: #00ccff;
    border-color: #00ccff;
}

QScrollBar:vertical {
    background-color: #3c3c3c;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}
"""

SHEETS_CHECKBOX_STYLE = """
QCheckBox {
    color: #ffffff;
    font-size: 14px;
    spacing: 8px;
    background-color: transparent;
    padding: 6px 4px;
    border-radius: 4px;
}

QCheckBox:hover {
    background-color: #252525;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #555555;
    border-radius: 3px;
    background-color: #2b2b2b;
}

QCheckBox::indicator:hover {
    border-color: #00aaff;
    background-color: #333333;
}

QCheckBox::indicator:checked {
    background-color: #00aaff;
    border-color: #00aaff;
    image: none;
}

QCheckBox::indicator:checked:hover {
    background-color: #00ccff;
    border-color: #00ccff;
}
"""
