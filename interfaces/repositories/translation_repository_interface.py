"""
Translation repository interface for translation operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional

from domain.entities.translation import Translation, BatchTranslation, TranslationRequest


class TranslationRepositoryInterface(ABC):
    """Interface for translation repository operations."""
    
    @abstractmethod
    async def save_translation(self, translation: Translation) -> bool:
        """Save a translation record."""
        ...
    
    @abstractmethod
    async def get_translation(self, translation_id: str) -> Optional[Translation]:
        """Get a translation by ID."""
        ...
    
    @abstractmethod
    async def get_translations_by_file(self, file_name: str) -> List[Translation]:
        """Get all translations for a specific file."""
        ...
    
    @abstractmethod
    async def save_batch_translation(self, batch: BatchTranslation) -> bool:
        """Save a batch translation record."""
        ...
    
    @abstractmethod
    async def get_batch_translation(self, batch_id: str) -> Optional[BatchTranslation]:
        """Get a batch translation by ID."""
        ...
    
    @abstractmethod
    async def update_translation_status(self, translation_id: str, status: str) -> bool:
        """Update translation status."""
        ...
    
    @abstractmethod
    async def delete_translation(self, translation_id: str) -> bool:
        """Delete a translation record."""
        ...
    
    @abstractmethod
    async def get_pending_translations(self) -> List[Translation]:
        """Get all pending translations."""
        ...
    
    @abstractmethod
    async def get_failed_translations(self) -> List[Translation]:
        """Get all failed translations."""
        ...
    
    @abstractmethod
    async def cleanup_old_translations(self, days: int) -> int:
        """Clean up translations older than specified days."""
        ...
