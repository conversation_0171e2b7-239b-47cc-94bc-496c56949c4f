"""
Input styling for the Translation Application
"""

SPINBOX_STYLE = """
QSpinBox {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 12px;
    min-width: 80px;
}

QSpinBox:hover {
    border-color: #00aaff;
    background-color: #4a4a4a;
}

QSpinBox:focus {
    border-color: #00ccff;
    outline: none;
}

QSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #555555;
    border-top-right-radius: 6px;
    background-color: #3c3c3c;
}

QSpinBox::up-button:hover {
    background-color: #4a4a4a;
}

QSpinBox::up-button:pressed {
    background-color: #2a2a2a;
}

QSpinBox::up-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 5px solid #cccccc;
    margin: 0px 2px;
}

QSpinBox::up-arrow:hover {
    border-bottom-color: #ffffff;
}

QSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 20px;
    border-left: 1px solid #555555;
    border-bottom-right-radius: 6px;
    background-color: #3c3c3c;
}

QSpinBox::down-button:hover {
    background-color: #4a4a4a;
}

QSpinBox::down-button:pressed {
    background-color: #2a2a2a;
}

QSpinBox::down-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 5px solid #cccccc;
    margin: 0px 2px;
}

QSpinBox::down-arrow:hover {
    border-top-color: #ffffff;
}
"""

# Batch Size Input Specific Styles
BATCH_SIZE_INPUT_STYLE = """
QSpinBox {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 10px 40px 10px 15px;
    color: #f0f6fc;
    font-size: 13px;
    min-width: 120px;
    min-height: 20px;
}

QSpinBox:hover {
    border-color: #484f58;
    background-color: #30363d;
}

QSpinBox:focus {
    border-color: #666666;
    outline: none;
}

QSpinBox::up-button {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    height: 12px;
    border: none;
    border-radius: 10px;
    background-color: #30363d;
    margin-right: 10px;
    margin-top: 4px;
    margin-bottom: 1px;
}

QSpinBox::up-button:hover {
    background-color: #484f58;
}

QSpinBox::up-button:pressed {
    background-color: #21262d;
}

QSpinBox::down-button {
    subcontrol-origin: padding;
    subcontrol-position: bottom right;
    width: 20px;
    height: 12px;
    border: none;
    border-radius: 10px;
    background-color: #30363d;
    margin-right: 10px;
    margin-bottom: 4px;
    margin-top: 1px;
}

QSpinBox::down-button:hover {
    background-color: #484f58;
}

QSpinBox::down-button:pressed {
    background-color: #21262d;
}

QSpinBox::up-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 4px solid #666666;
    margin: 0px 2px;
}

QSpinBox::down-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 4px solid #666666;
    margin: 0px 2px;
}

QSpinBox::up-arrow:hover,
QSpinBox::down-arrow:hover {
    border-bottom-color: #f0f6fc;
    border-top-color: #f0f6fc;
}
"""

BATCH_SIZE_INPUT_STYLE = SPINBOX_STYLE
