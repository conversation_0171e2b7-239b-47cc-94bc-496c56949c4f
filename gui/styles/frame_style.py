"""
Frame and Card Styling

Styles for frames, cards, and container components.
"""

# Main Frame Styles
FRAME_STYLE = """
QFrame {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    margin: 2px;
}

QFrame#sectionFrame {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    margin: 2px;
}

QFrame#cardFrame {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    margin: 2px;
}
"""

# Legacy Card Frame Style (for compatibility)
CARD_FRAME_STYLE = """
QFrame#cardFrame {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 8px;
    margin: 2px;
}

QLabel#cardTitle {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    padding: 0px 0px 2px 0px;
    background-color: transparent;
}
"""

PROGRESS_CARD_STYLE = """
QFrame#progressCard {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 8px;
    margin: 2px;
    padding: 15px;
}
"""

LOGS_CARD_STYLE = """
QFrame#logsCard {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 8px;
    margin: 2px;
    padding: 15px;
}

QLabel#logsTitle {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    padding: 0px 0px 2px 0px;
    background-color: transparent;
}
"""

SECTION_CARD_STYLE = """
QFrame#sectionCard {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 8px;
    margin: 2px;
    padding: 15px;
}

QLabel#sectionCardTitle {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    padding: 0px 0px 2px 0px;
    background-color: transparent;
}
"""

# Modern Frame Style (recommended)
MODERN_FRAME_STYLE = """
QFrame#cardFrame {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    margin: 2px;
}

QLabel#cardTitle {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: bold;
    padding: 0px 0px 2px 0px;
    background-color: transparent;
}

QLabel#fieldLabel {
    color: #8b949e;
    font-size: 13px;
    margin-top: 5px;
    margin-bottom: 2px;
    background-color: transparent;
}
"""

# Legacy compatibility
CARD_FRAME_INLINE_STYLE = MODERN_FRAME_STYLE

# Combined frame styles
ALL_FRAME_STYLES = FRAME_STYLE + PROGRESS_CARD_STYLE + LOGS_CARD_STYLE + SECTION_CARD_STYLE

# Legacy compatibility
ALL_CARD_STYLES = ALL_FRAME_STYLES
