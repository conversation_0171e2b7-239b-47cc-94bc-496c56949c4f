from PyQt6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt


class SettingsWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        self.setWindowTitle("Settings")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title_label = QLabel("Application Settings")
        title_label.setObjectName("settingsTitle")
        layout.addWidget(title_label)
        
        # Settings content (placeholder)
        content_label = QLabel("Settings options will be implemented here.")
        content_label.setObjectName("settingsContent")
        layout.addWidget(content_label)
        
        # Spacer
        layout.addStretch()
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        ok_button = QPushButton("OK")
        ok_button.setObjectName("settingsButton")
        ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.setObjectName("settingsButton")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
    
    def apply_styles(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            QLabel#settingsTitle {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
            }
            
            QLabel#settingsContent {
                font-size: 12px;
                color: #cccccc;
            }
            
            QPushButton#settingsButton {
                background-color: #3c3c3c;
                border: 1px solid #00aaff;
                border-radius: 6px;
                color: #ffffff;
                font-size: 12px;
                padding: 8px 16px;
                min-width: 80px;
            }
            
            QPushButton#settingsButton:hover {
                background-color: #4a4a4a;
                border-color: #00ccff;
            }
            
            QPushButton#settingsButton:pressed {
                background-color: #2a2a2a;
            }
        """)
