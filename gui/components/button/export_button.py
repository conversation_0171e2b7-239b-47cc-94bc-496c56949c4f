

from PyQt6.QtWidgets import QPushButton
from ...styles.export_button_style import EXPORT_BUTTON_STYLE, EXPORT_BUTTON_ENABLED_STYLE
from ...managers.localization_manager import get_localization_manager, tr


class ExportButton(QPushButton):
    def __init__(self):
        super().__init__()
        self.localization_manager = get_localization_manager()
        self.setup_ui()
        self.apply_styles()

        # Connect to language change signal
        self.localization_manager.language_changed.connect(self.update_text)

    def setup_ui(self):
        self.setText(tr("export_button"))
        self.setFixedHeight(40)
        self.setMinimumWidth(200)

    def apply_styles(self):
        self.setStyleSheet(EXPORT_BUTTON_STYLE)

    def apply_enabled_styles(self):
        """Apply blue border styling when export is available after translation completion."""
        self.setStyleSheet(EXPORT_BUTTON_ENABLED_STYLE)

    def reset_styles(self):
        """Reset to default styling."""
        self.setStyleSheet(EXPORT_BUTTON_STYLE)

    def update_text(self):
        """Update button text when language changes."""
        self.setText(tr("export_button"))
