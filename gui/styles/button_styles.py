"""
<PERSON>ton Styling

Styles for all button components in the Translation Application.
"""

# Default Button Style
BUTTON_STYLE = """
QPushButton {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 12px;
    font-weight: bold;
    padding: 10px 20px;
}

QPushButton:hover {
    background-color: #30363d;
    border-color: #484f58;
}

QPushButton:pressed {
    background-color: #161b22;
    border-color: #21262d;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #555555;
    color: #888888;
}
"""

# Translate Button Style (Green accent)
TRANSLATE_BUTTON_STYLE = """
QPushButton {
    background-color: #21262d;
    border: 1px solid #238636;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 13px;
    font-weight: normal;
    padding: 12px 24px;
    min-width: 120px;
}

QPushButton:hover {
    background-color: #30363d;
    border: 2px solid #2ea043;
}

QPushButton:pressed {
    background-color: #161b22;
    border: 2px solid #238636;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #555555;
    color: #888888;
}
"""

# Cancel Button Style (Red accent)
CANCEL_BUTTON_STYLE = """
QPushButton {
    background-color: #21262d;
    border: 1px solid #da3633;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 13px;
    font-weight: normal;
    padding: 12px 24px;
    min-width: 120px;
}

QPushButton:hover {
    background-color: #30363d;
    border: 2px solid #f85149;
}

QPushButton:pressed {
    background-color: #161b22;
    border: 2px solid #da3633;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #555555;
    color: #888888;
}
"""

# Export Button Style (Default accent)
EXPORT_BUTTON_STYLE = """
QPushButton {
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 12px;
    font-weight: bold;
    padding: 10px 20px;
    min-width: 200px;
}

QPushButton:hover {
    background-color: #30363d;
    border-color: #484f58;
}

QPushButton:pressed {
    background-color: #161b22;
    border-color: #21262d;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #555555;
    color: #888888;
}
"""

# Legacy compatibility
EXPORT_BUTTON_INLINE_STYLE = EXPORT_BUTTON_STYLE
