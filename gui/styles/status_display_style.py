"""
Status Display Component Styling

Styles for the status display component with midnight dark theme.
"""

STATUS_DISPLAY_STYLE = """
QWidget {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 5px;
}

QLabel#statusMessage {
    background-color: transparent;
    color: #58a6ff;
    font-size: 11px;
    font-weight: bold;
    padding: 8px 12px;
    border: none;
    text-align: center;
}

/* Different colors for different status types */
QLabel#statusMessage[status="welcome"] {
    color: #7c3aed;
}

QLabel#statusMessage[status="started"] {
    color: #f59e0b;
}

QLabel#statusMessage[status="completed"] {
    color: #10b981;
}

QLabel#statusMessage[status="cancelled"] {
    color: #ef4444;
}

QLabel#statusMessage[status="exported"] {
    color: #06b6d4;
}

QLabel#statusMessage[status="ready"] {
    color: #6b7280;
}
"""
