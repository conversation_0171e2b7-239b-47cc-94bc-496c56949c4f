"""
Translation validator implementation.
"""

from typing import List, Dict, Any
import re

from interfaces.validators.validation_interface import TranslationValidationInterface


class TranslationValidatorImpl(TranslationValidationInterface):
    """Implementation of translation validation interface."""
    
    def __init__(self):
        """Initialize the translation validator."""
        self.errors = []
        self.warnings = []
        
        # Valid language codes (subset of common ones)
        self.valid_language_codes = {
            'auto', 'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko',
            'zh', 'ar', 'hi', 'vi', 'th', 'id', 'tl', 'nl', 'pl', 'tr',
            'cs', 'hu', 'sv', 'da', 'no', 'fi', 'el', 'he', 'fa', 'ur',
            'bn', 'ta', 'te', 'gu', 'kn', 'ml', 'mr', 'pa', 'si', 'my',
            'km', 'lo', 'ka', 'am', 'sw', 'zu', 'af', 'sq', 'hy', 'az',
            'eu', 'be', 'bs', 'bg', 'ca', 'hr', 'et', 'lv', 'lt', 'mk',
            'mt', 'ro', 'sk', 'sl', 'uk', 'cy', 'ga', 'is', 'lb'
        }
    
    def validate(self, data: Any) -> bool:
        """Validate translation data."""
        if isinstance(data, str):
            return self.validate_language_code(data)
        elif isinstance(data, dict):
            return self._validate_translation_dict(data)
        return False
    
    def validate_language_code(self, language_code: str) -> bool:
        """Validate language code."""
        self.errors.clear()
        self.warnings.clear()
        
        if not language_code:
            self.errors.append("Language code cannot be empty")
            return False
        
        normalized_code = language_code.lower().strip()
        
        # Handle language-region codes (e.g., en-US -> en)
        if '-' in normalized_code:
            base_code = normalized_code.split('-')[0]
            if base_code in self.valid_language_codes:
                if normalized_code not in ['zh-cn', 'zh-tw', 'pt-br']:
                    self.warnings.append(f"Using base language code '{base_code}' for '{language_code}'")
                return True
        
        if normalized_code not in self.valid_language_codes:
            self.errors.append(f"Invalid language code: {language_code}")
            return False
        
        return True
    
    def validate_text_length(self, text: str, max_length: int) -> bool:
        """Validate text length."""
        if not text:
            self.errors.append("Text cannot be empty")
            return False
        
        if len(text) > max_length:
            self.errors.append(f"Text too long: {len(text)} > {max_length} characters")
            return False
        
        if len(text) > max_length * 0.8:
            self.warnings.append(f"Text is long: {len(text)} characters")
        
        return True
    
    def validate_api_key(self, api_key: str, service_name: str) -> bool:
        """Validate API key format."""
        if not api_key:
            self.errors.append(f"API key for {service_name} cannot be empty")
            return False
        
        if service_name.lower() == 'google':
            return self._validate_google_api_key(api_key)
        elif service_name.lower() == 'deepl':
            return self._validate_deepl_api_key(api_key)
        else:
            self.warnings.append(f"Unknown service: {service_name}")
            return True
    
    def _validate_google_api_key(self, api_key: str) -> bool:
        """Validate Google API key format."""
        # Google API keys are typically 39 characters
        if len(api_key) < 35:
            self.errors.append("Google API key appears to be too short")
            return False
        
        # Basic format check (alphanumeric with some special chars)
        if not re.match(r'^[A-Za-z0-9_-]+$', api_key):
            self.warnings.append("Google API key format may be invalid")
        
        return True
    
    def _validate_deepl_api_key(self, api_key: str) -> bool:
        """Validate DeepL API key format."""
        # DeepL API keys end with :fx for free or :px for pro
        if not api_key.endswith((':fx', ':px')):
            self.errors.append("DeepL API key must end with :fx (free) or :px (pro)")
            return False
        
        # Check minimum length
        if len(api_key) < 35:
            self.errors.append("DeepL API key appears to be too short")
            return False
        
        return True
    
    def _validate_translation_dict(self, data: Dict[str, Any]) -> bool:
        """Validate translation dictionary."""
        required_fields = ['text', 'source_language', 'target_language']
        
        for field in required_fields:
            if field not in data:
                self.errors.append(f"Missing required field: {field}")
                return False
        
        # Validate individual fields
        if not self.validate_text_length(data['text'], 10000):
            return False
        
        if not self.validate_language_code(data['source_language']):
            return False
        
        if not self.validate_language_code(data['target_language']):
            return False
        
        # Check if source and target are the same
        if data['source_language'].lower() == data['target_language'].lower():
            self.warnings.append("Source and target languages are the same")
        
        return True
    
    def get_validation_errors(self) -> List[str]:
        """Get validation error messages."""
        return self.errors.copy()
    
    def get_validation_warnings(self) -> List[str]:
        """Get validation warning messages."""
        return self.warnings.copy()
    
    def reset_validation_state(self) -> None:
        """Reset validation state."""
        self.errors.clear()
        self.warnings.clear()
