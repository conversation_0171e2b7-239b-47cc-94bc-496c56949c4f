"""
Excel file domain entity representing an Excel workbook and its properties.
"""

from typing import List, Optional, Dict, Any
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class ExcelSheet:
    """Represents a single sheet within an Excel file."""
    
    name: str
    index: int
    row_count: int
    column_count: int
    has_data: bool = False
    translatable_cells: List[tuple] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExcelFile:
    """Domain entity representing an Excel file with all its properties."""
    
    file_path: Path
    file_name: str
    file_size: int
    sheets: List[ExcelSheet] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: Optional[datetime] = None
    is_valid: bool = False
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post initialization processing."""
        if isinstance(self.file_path, str):
            self.file_path = Path(self.file_path)
        
        if not self.file_name:
            self.file_name = self.file_path.name
    
    @property
    def total_sheets(self) -> int:
        """Get total number of sheets."""
        return len(self.sheets)
    
    @property
    def total_translatable_cells(self) -> int:
        """Get total number of translatable cells across all sheets."""
        return sum(len(sheet.translatable_cells) for sheet in self.sheets)
    
    @property
    def file_extension(self) -> str:
        """Get file extension."""
        return self.file_path.suffix.lower()
    
    @property
    def is_xlsx(self) -> bool:
        """Check if file is .xlsx format."""
        return self.file_extension == '.xlsx'
    
    @property
    def is_xls(self) -> bool:
        """Check if file is .xls format."""
        return self.file_extension == '.xls'
    
    def add_sheet(self, sheet: ExcelSheet) -> None:
        """Add a sheet to the file."""
        self.sheets.append(sheet)
    
    def get_sheet_by_name(self, name: str) -> Optional[ExcelSheet]:
        """Get sheet by name."""
        for sheet in self.sheets:
            if sheet.name == name:
                return sheet
        return None
    
    def get_sheet_by_index(self, index: int) -> Optional[ExcelSheet]:
        """Get sheet by index."""
        for sheet in self.sheets:
            if sheet.index == index:
                return sheet
        return None
    
    def validate(self) -> bool:
        """Validate the Excel file."""
        try:
            if not self.file_path.exists():
                self.error_message = "File does not exist"
                self.is_valid = False
                return False
            
            if self.file_extension not in ['.xlsx', '.xls']:
                self.error_message = "Unsupported file format"
                self.is_valid = False
                return False
            
            if self.file_size == 0:
                self.error_message = "File is empty"
                self.is_valid = False
                return False
            
            self.is_valid = True
            self.error_message = None
            return True
            
        except Exception as e:
            self.error_message = str(e)
            self.is_valid = False
            return False
