"""
Application settings and configuration.
"""

import os
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass, field


@dataclass
class APISettings:
    """API-related settings."""
    google_api_key: str = ""
    deepl_api_key: str = ""
    default_translation_engine: str = "deepl"
    request_timeout: int = 30
    max_retries: int = 3
    rate_limit_delay: float = 0.1


@dataclass
class FileSettings:
    """File-related settings."""
    supported_formats: List[str] = field(default_factory=lambda: ['.xlsx', '.xls'])
    max_file_size_mb: float = 50.0
    backup_enabled: bool = True
    backup_directory: str = "backups"
    output_directory: str = "output"


@dataclass
class TranslationSettings:
    """Translation-related settings."""
    default_source_language: str = "auto"
    default_target_language: str = "en"
    default_batch_size: int = 50
    max_batch_size: int = 1000
    preserve_formatting: bool = True
    ignore_brackets: bool = True
    ignore_formulas: bool = True
    ignore_numbers: bool = True


@dataclass
class UISettings:
    """UI-related settings."""
    window_width: int = 1200
    window_height: int = 800
    theme: str = "light"
    language: str = "en"
    font_family: str = "Arial"
    font_size: int = 10
    auto_save_settings: bool = True


@dataclass
class LoggingSettings:
    """Logging-related settings."""
    log_level: str = "INFO"
    log_to_file: bool = True
    log_file_path: str = "logs/app.log"
    max_log_file_size_mb: float = 10.0
    log_retention_days: int = 30


@dataclass
class ApplicationSettings:
    """Main application settings."""
    api: APISettings = field(default_factory=APISettings)
    file: FileSettings = field(default_factory=FileSettings)
    translation: TranslationSettings = field(default_factory=TranslationSettings)
    ui: UISettings = field(default_factory=UISettings)
    logging: LoggingSettings = field(default_factory=LoggingSettings)
    
    @classmethod
    def load_from_env(cls) -> 'ApplicationSettings':
        """Load settings from environment variables."""
        settings = cls()
        
        # API settings
        settings.api.google_api_key = os.getenv('GOOGLE_TRANSLATE_API_KEY', '')
        settings.api.deepl_api_key = os.getenv('DEEPL_API_KEY', '')
        settings.api.default_translation_engine = os.getenv('DEFAULT_TRANSLATION_ENGINE', 'deepl')
        settings.api.request_timeout = int(os.getenv('REQUEST_TIMEOUT', '30'))
        settings.api.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        
        # File settings
        settings.file.max_file_size_mb = float(os.getenv('MAX_FILE_SIZE_MB', '50.0'))
        settings.file.backup_enabled = os.getenv('BACKUP_ENABLED', 'True').lower() == 'true'
        settings.file.backup_directory = os.getenv('BACKUP_DIRECTORY', 'backups')
        settings.file.output_directory = os.getenv('OUTPUT_DIRECTORY', 'output')
        
        # Translation settings
        settings.translation.default_source_language = os.getenv('DEFAULT_SOURCE_LANGUAGE', 'auto')
        settings.translation.default_target_language = os.getenv('DEFAULT_TARGET_LANGUAGE', 'en')
        settings.translation.default_batch_size = int(os.getenv('DEFAULT_BATCH_SIZE', '50'))
        settings.translation.max_batch_size = int(os.getenv('MAX_BATCH_SIZE', '1000'))
        
        # Logging settings
        settings.logging.log_level = os.getenv('LOG_LEVEL', 'INFO')
        settings.logging.log_to_file = os.getenv('LOG_TO_FILE', 'True').lower() == 'true'
        settings.logging.log_file_path = os.getenv('LOG_FILE_PATH', 'logs/app.log')
        
        return settings
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return {
            'api': {
                'google_api_key': self.api.google_api_key,
                'deepl_api_key': self.api.deepl_api_key,
                'default_translation_engine': self.api.default_translation_engine,
                'request_timeout': self.api.request_timeout,
                'max_retries': self.api.max_retries,
                'rate_limit_delay': self.api.rate_limit_delay
            },
            'file': {
                'supported_formats': self.file.supported_formats,
                'max_file_size_mb': self.file.max_file_size_mb,
                'backup_enabled': self.file.backup_enabled,
                'backup_directory': self.file.backup_directory,
                'output_directory': self.file.output_directory
            },
            'translation': {
                'default_source_language': self.translation.default_source_language,
                'default_target_language': self.translation.default_target_language,
                'default_batch_size': self.translation.default_batch_size,
                'max_batch_size': self.translation.max_batch_size,
                'preserve_formatting': self.translation.preserve_formatting,
                'ignore_brackets': self.translation.ignore_brackets,
                'ignore_formulas': self.translation.ignore_formulas,
                'ignore_numbers': self.translation.ignore_numbers
            },
            'ui': {
                'window_width': self.ui.window_width,
                'window_height': self.ui.window_height,
                'theme': self.ui.theme,
                'language': self.ui.language,
                'font_family': self.ui.font_family,
                'font_size': self.ui.font_size,
                'auto_save_settings': self.ui.auto_save_settings
            },
            'logging': {
                'log_level': self.logging.log_level,
                'log_to_file': self.logging.log_to_file,
                'log_file_path': self.logging.log_file_path,
                'max_log_file_size_mb': self.logging.max_log_file_size_mb,
                'log_retention_days': self.logging.log_retention_days
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApplicationSettings':
        """Create settings from dictionary."""
        settings = cls()
        
        if 'api' in data:
            api_data = data['api']
            settings.api = APISettings(**api_data)
        
        if 'file' in data:
            file_data = data['file']
            settings.file = FileSettings(**file_data)
        
        if 'translation' in data:
            translation_data = data['translation']
            settings.translation = TranslationSettings(**translation_data)
        
        if 'ui' in data:
            ui_data = data['ui']
            settings.ui = UISettings(**ui_data)
        
        if 'logging' in data:
            logging_data = data['logging']
            settings.logging = LoggingSettings(**logging_data)
        
        return settings
    
    def save_to_file(self, file_path: str) -> bool:
        """Save settings to JSON file."""
        try:
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            return True
        except Exception:
            return False
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'ApplicationSettings':
        """Load settings from JSON file."""
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except Exception:
            return cls()
    
    def create_directories(self) -> None:
        """Create necessary directories."""
        directories = [
            self.file.backup_directory,
            self.file.output_directory,
            Path(self.logging.log_file_path).parent
        ]
        
        for directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
            except Exception:
                pass
    
    def validate(self) -> Dict[str, List[str]]:
        """Validate settings and return any issues."""
        issues = {
            'errors': [],
            'warnings': []
        }

        # Validate API settings with detailed validation
        api_validation_result = self._validate_api_keys()
        issues['errors'].extend(api_validation_result['errors'])
        issues['warnings'].extend(api_validation_result['warnings'])

        if self.api.default_translation_engine not in ['google', 'deepl']:
            issues['errors'].append('Invalid default translation engine')
        
        # Validate file settings
        if self.file.max_file_size_mb <= 0:
            issues['errors'].append('Invalid max file size')
        
        # Validate translation settings
        if self.translation.default_batch_size <= 0 or self.translation.default_batch_size > self.translation.max_batch_size:
            issues['errors'].append('Invalid batch size settings')
        
        # Validate UI settings
        if self.ui.window_width < 800 or self.ui.window_height < 600:
            issues['warnings'].append('Small window size may affect usability')
        
        return issues

    def _validate_api_keys(self) -> Dict[str, List[str]]:
        """Validate API keys with detailed error messages."""
        validation_issues = {
            'errors': [],
            'warnings': []
        }

        google_key = self.api.google_api_key.strip() if self.api.google_api_key else ""
        deepl_key = self.api.deepl_api_key.strip() if self.api.deepl_api_key else ""

        # Check if any API keys are configured
        if not google_key and not deepl_key:
            validation_issues['warnings'].append(
                'No translation API keys configured. Please add API keys in Preferences → Settings → API Keys to enable translation functionality.'
            )
            return validation_issues

        # Import validator
        try:
            from infrastructure.validators.translation_validator_impl import TranslationValidatorImpl
            validator = TranslationValidatorImpl()
        except ImportError:
            validation_issues['warnings'].append('Could not load API key validator')
            return validation_issues

        # Validate Google API key if provided
        if google_key:
            if not validator.validate_api_key(google_key, 'google'):
                for error in validator.errors:
                    validation_issues['errors'].append(f"Google API key issue: {error}")
                for warning in validator.warnings:
                    validation_issues['warnings'].append(f"Google API key warning: {warning}")
                validator.errors.clear()
                validator.warnings.clear()

        # Validate DeepL API key if provided
        if deepl_key:
            if not validator.validate_api_key(deepl_key, 'deepl'):
                for error in validator.errors:
                    validation_issues['errors'].append(f"DeepL API key issue: {error}")
                for warning in validator.warnings:
                    validation_issues['warnings'].append(f"DeepL API key warning: {warning}")
                validator.errors.clear()
                validator.warnings.clear()

        # Check if at least one valid API key exists
        valid_google = google_key and validator.validate_api_key(google_key, 'google')
        valid_deepl = deepl_key and validator.validate_api_key(deepl_key, 'deepl')

        if not valid_google and not valid_deepl:
            if google_key or deepl_key:
                validation_issues['warnings'].append(
                    'No valid translation API keys found. Please check your API keys in Preferences → Settings → API Keys.'
                )

        return validation_issues


# Global settings instance
settings = ApplicationSettings.load_from_env()
