from PyQt6.QtWidgets import Q<PERSON>pinBox
from PyQt6.QtCore import Qt, pyqtSignal
from ...styles.batch_size_input_style import BATCH_SIZE_INPUT_STYLE


class BatchSizeInput(QSpinBox):
    batch_size_changed = pyqtSignal(int, int)  # (old_value, new_value)
    
    def __init__(self):
        super().__init__()
        self.previous_value = 50  # Track previous value for change detection
        self.setup_ui()
        self.apply_styles()
        self.connect_signals()

    def setup_ui(self):
        self.setFixedHeight(35)
        self.setMinimum(1)
        self.setMaximum(1000)
        self.setValue(50)
        self.setSingleStep(1)
        self.setAlignment(Qt.AlignmentFlag.AlignLeft)
    
    def apply_styles(self):
        self.setStyleSheet(BATCH_SIZE_INPUT_STYLE)
    
    def connect_signals(self):
        """Connect value change signals"""
        self.valueChanged.connect(self.on_value_changed)
    
    def on_value_changed(self, new_value):
        """Handle batch size value changes"""
        old_value = self.previous_value
        if old_value != new_value:
            self.batch_size_changed.emit(old_value, new_value)
            self.previous_value = new_value
    
    def set_value_silently(self, value):
        """Set value without triggering change signals"""
        self.blockSignals(True)
        self.setValue(value)
        self.previous_value = value
        self.blockSignals(False)
