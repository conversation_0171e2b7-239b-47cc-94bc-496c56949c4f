"""
Scroll Bar Styling

Custom scroll bar styles for vertical and horizontal scroll bars.
"""

# Main Scroll Bar Style
SCROLLBAR_STYLE = """
/* Vertical Scroll Bar */
QScrollBar:vertical {
    background-color: #161b22;
    width: 12px;
    border-radius: 3px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #30363d;
    border-radius: 3px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #484f58;
    border-radius: 3px;
}

QScrollBar::handle:vertical:pressed {
    background-color: #21262d;
    border-radius: 6px;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal Scroll Bar */
QScrollBar:horizontal {
    background-color: #161b22;
    height: 12px;
    border-radius: 3px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #30363d;
    border-radius: 3px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #484f58;
    border-radius: 3px;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #21262d;
    border-radius: 6px;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}
"""

# Scroll Area Styles
SCROLL_AREA_STYLE = """
QScrollArea {
    border: 1px solid #30363d;
    border-radius: 4px;
    background-color: #0d1117;
}

QScrollArea > QWidget > QWidget {
    background-color: #0d1117;
}
"""
