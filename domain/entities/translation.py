"""
Translation domain entity representing translation operations and results.
"""

from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class TranslationStatus(Enum):
    """Translation status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TranslationType(Enum):
    """Translation type enumeration."""
    BATCH = "batch"
    SINGLE = "single"
    AUTO_DETECT = "auto_detect"


@dataclass
class TranslationRequest:
    """Represents a translation request."""
    
    text: str
    source_language: str
    target_language: str
    translation_type: TranslationType = TranslationType.SINGLE
    preserve_formatting: bool = True
    ignore_brackets: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TranslationResult:
    """Represents the result of a translation operation."""
    
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    confidence_score: Optional[float] = None
    detected_language: Optional[str] = None
    translation_engine: Optional[str] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Translation:
    """Domain entity representing a complete translation operation."""
    
    translation_id: str
    request: TranslationRequest
    result: Optional[TranslationResult] = None
    status: TranslationStatus = TranslationStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    cell_location: Optional[tuple] = None
    sheet_name: Optional[str] = None
    file_name: Optional[str] = None
    error_details: Optional[str] = None
    
    def __post_init__(self):
        """Post initialization processing."""
        if not self.translation_id:
            self.translation_id = self._generate_id()
    
    def _generate_id(self) -> str:
        """Generate a unique translation ID."""
        import uuid
        return str(uuid.uuid4())
    
    @property
    def is_completed(self) -> bool:
        """Check if translation is completed."""
        return self.status == TranslationStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if translation failed."""
        return self.status == TranslationStatus.FAILED
    
    @property
    def is_pending(self) -> bool:
        """Check if translation is pending."""
        return self.status == TranslationStatus.PENDING
    
    @property
    def is_in_progress(self) -> bool:
        """Check if translation is in progress."""
        return self.status == TranslationStatus.IN_PROGRESS
    
    @property
    def duration(self) -> Optional[float]:
        """Get translation duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def can_retry(self) -> bool:
        """Check if translation can be retried."""
        return self.retry_count < self.max_retries and self.is_failed
    
    def start(self) -> None:
        """Mark translation as started."""
        self.status = TranslationStatus.IN_PROGRESS
        self.started_at = datetime.now()
    
    def complete(self, result: TranslationResult) -> None:
        """Mark translation as completed."""
        self.status = TranslationStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
    
    def fail(self, error_message: str) -> None:
        """Mark translation as failed."""
        self.status = TranslationStatus.FAILED
        self.completed_at = datetime.now()
        self.error_details = error_message
    
    def cancel(self) -> None:
        """Cancel the translation."""
        self.status = TranslationStatus.CANCELLED
        self.completed_at = datetime.now()
    
    def retry(self) -> bool:
        """Retry the translation."""
        if self.can_retry:
            self.retry_count += 1
            self.status = TranslationStatus.PENDING
            self.started_at = None
            self.completed_at = None
            self.error_details = None
            return True
        return False


@dataclass
class BatchTranslation:
    """Represents a batch of translations."""
    
    batch_id: str
    translations: List[Translation] = field(default_factory=list)
    batch_size: int = 50
    created_at: datetime = field(default_factory=datetime.now)
    status: TranslationStatus = TranslationStatus.PENDING
    progress: float = 0.0
    
    def __post_init__(self):
        """Post initialization processing."""
        if not self.batch_id:
            self.batch_id = self._generate_batch_id()
    
    def _generate_batch_id(self) -> str:
        """Generate a unique batch ID."""
        import uuid
        return f"batch_{uuid.uuid4()}"
    
    @property
    def total_translations(self) -> int:
        """Get total number of translations in batch."""
        return len(self.translations)
    
    @property
    def completed_translations(self) -> int:
        """Get number of completed translations."""
        return len([t for t in self.translations if t.is_completed])
    
    @property
    def failed_translations(self) -> int:
        """Get number of failed translations."""
        return len([t for t in self.translations if t.is_failed])
    
    @property
    def progress_percentage(self) -> float:
        """Get progress as percentage."""
        if self.total_translations == 0:
            return 0.0
        return (self.completed_translations / self.total_translations) * 100.0
    
    def add_translation(self, translation: Translation) -> None:
        """Add a translation to the batch."""
        self.translations.append(translation)
    
    def update_progress(self) -> None:
        """Update batch progress."""
        self.progress = self.progress_percentage
        
        if self.completed_translations == self.total_translations:
            self.status = TranslationStatus.COMPLETED
        elif self.failed_translations == self.total_translations:
            self.status = TranslationStatus.FAILED
        elif any(t.is_in_progress for t in self.translations):
            self.status = TranslationStatus.IN_PROGRESS
