"""
Validation interface for various validation operations.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pathlib import Path


class ValidationInterface(ABC):
    """Interface for validation operations."""
    
    @abstractmethod
    def validate(self, data: Any) -> bool:
        """Validate the given data."""
        ...
    
    @abstractmethod
    def get_validation_errors(self) -> List[str]:
        """Get validation error messages."""
        ...
    
    @abstractmethod
    def get_validation_warnings(self) -> List[str]:
        """Get validation warning messages."""
        ...
    
    @abstractmethod
    def reset_validation_state(self) -> None:
        """Reset validation state."""
        ...


class FileValidationInterface(ValidationInterface):
    """Interface for file validation operations."""
    
    @abstractmethod
    def validate_file_path(self, file_path: Path) -> bool:
        """Validate file path."""
        ...
    
    @abstractmethod
    def validate_file_size(self, file_path: Path, max_size_mb: float) -> bool:
        """Validate file size."""
        ...
    
    @abstractmethod
    def validate_file_format(self, file_path: Path, allowed_formats: List[str]) -> bool:
        """Validate file format."""
        ...


class TranslationValidationInterface(ValidationInterface):
    """Interface for translation validation operations."""
    
    @abstractmethod
    def validate_language_code(self, language_code: str) -> bool:
        """Validate language code."""
        ...
    
    @abstractmethod
    def validate_text_length(self, text: str, max_length: int) -> bool:
        """Validate text length."""
        ...
    
    @abstractmethod
    def validate_api_key(self, api_key: str, service_name: str) -> bool:
        """Validate API key format."""
        ...


class ConfigurationValidationInterface(ValidationInterface):
    """Interface for configuration validation operations."""
    
    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate configuration dictionary."""
        ...
    
    @abstractmethod
    def validate_required_fields(self, config: Dict[str, Any], required_fields: List[str]) -> bool:
        """Validate required configuration fields."""
        ...
    
    @abstractmethod
    def validate_environment_variables(self, env_vars: List[str]) -> bool:
        """Validate environment variables."""
        ...
