"""
Localization Manager for instant language switching.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal


class LocalizationManager(QObject):
    """Manager for handling application localization and instant language switching."""
    
    # Signal emitted when language changes
    language_changed = pyqtSignal(str)  # language_code
    
    def __init__(self):
        """Initialize the localization manager."""
        super().__init__()
        self._current_language = "en"
        self._translations: Dict[str, Dict[str, str]] = {}
        self._supported_languages = {
            "en": "English",
            "ja": "Japanese", 
            "vi": "Vietnamese"
        }
        self._load_all_translations()
    
    def _load_all_translations(self):
        """Load all translation files."""
        translations_dir = Path(__file__).parent.parent.parent / "resources" / "translations"
        
        for lang_code in self._supported_languages.keys():
            translation_file = translations_dir / f"{lang_code}.json"
            if translation_file.exists():
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self._translations[lang_code] = json.load(f)
                except Exception as e:
                    print(f"Error loading translation file {translation_file}: {e}")
                    self._translations[lang_code] = {}
            else:
                self._translations[lang_code] = {}
    
    @property
    def current_language(self) -> str:
        """Get the current language code."""
        return self._current_language
    
    @property
    def supported_languages(self) -> Dict[str, str]:
        """Get supported languages mapping with localized names."""
        # Return localized interface language names
        localized_names = {}
        for code in self._supported_languages.keys():
            if code == "en":
                localized_names[code] = self.get_text("english")
            elif code == "ja":
                localized_names[code] = self.get_text("japanese")
            elif code == "vi":
                localized_names[code] = self.get_text("vietnamese")
            else:
                localized_names[code] = self._supported_languages[code]
        return localized_names
    
    def set_language(self, language_code: str) -> bool:
        """Set the current language and emit change signal."""
        if language_code not in self._supported_languages:
            return False
        
        if self._current_language != language_code:
            self._current_language = language_code
            self.language_changed.emit(language_code)
        
        return True
    
    def get_text(self, key: str, default: Optional[str] = None) -> str:
        """Get translated text for the current language."""
        if self._current_language in self._translations:
            translation = self._translations[self._current_language].get(key)
            if translation:
                return translation
        
        # Fallback to English if current language doesn't have the key
        if self._current_language != "en" and "en" in self._translations:
            translation = self._translations["en"].get(key)
            if translation:
                return translation
        
        # Return default or key if no translation found
        return default if default is not None else key
    
    def get_language_name(self, language_code: str) -> str:
        """Get the display name for a language code."""
        return self._supported_languages.get(language_code, language_code)


# Global instance
_localization_manager = None


def get_localization_manager() -> LocalizationManager:
    """Get the global localization manager instance."""
    global _localization_manager
    if _localization_manager is None:
        _localization_manager = LocalizationManager()
    return _localization_manager


def tr(key: str, default: Optional[str] = None) -> str:
    """Convenience function for getting translated text."""
    return get_localization_manager().get_text(key, default)
