"""
File service interface for file operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from pathlib import Path

from domain.entities.excel_file import ExcelFile


class FileServiceInterface(ABC):
    """Interface for file service operations."""
    
    @abstractmethod
    async def process_file(self, file_path: Path) -> Optional[ExcelFile]:
        """Process an Excel file and extract translatable content."""
        ...
    
    @abstractmethod
    async def save_translated_file(self, excel_file: ExcelFile, output_path: Path) -> bool:
        """Save translated Excel file with preserved formatting."""
        ...
    
    @abstractmethod
    async def validate_file_format(self, file_path: Path) -> bool:
        """Validate if the file format is supported."""
        ...
    
    @abstractmethod
    async def extract_text_cells(self, excel_file: ExcelFile) -> List[Dict[str, Any]]:
        """Extract text cells that need translation."""
        ...
    
    @abstractmethod
    async def apply_translations(self, excel_file: ExcelFile, translations: Dict[str, str]) -> bool:
        """Apply translations to the Excel file."""
        ...
    
    @abstractmethod
    async def preserve_formatting(self, source_file: ExcelFile, target_file: ExcelFile) -> bool:
        """Preserve formatting from source to target file."""
        ...
    
    @abstractmethod
    async def backup_original_file(self, file_path: Path) -> Optional[Path]:
        """Create a backup of the original file."""
        ...
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """Get supported file formats."""
        ...
