"""
Main Application Styling

Core application styles including main window, central widget,
and general application-wide styling.
"""

# Main Application Style
APPLICATION_STYLE = """
/* Main Window */
QMainWindow {
    background-color: #0d1117;
    color: #f0f6fc;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
}

/* Central Widget */
QWidget {
    background-color: #0d1117;
    color: #f0f6fc;
}

/* General Widget Styling */
QWidget {
    background-color: transparent;
    color: #f0f6fc;
}

/* Labels */
QLabel {
    color: #f0f6fc;
    background-color: transparent;
}

QLabel#cardTitle {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: bold;
    padding: 0px 0px 2px 0px;
    background-color: transparent;
}

QLabel#fieldLabel {
    color: #8b949e;
    font-size: 13px;
    margin-top: 5px;
    margin-bottom: 2px;
    background-color: transparent;
}

/* Menu Bar Styling */
QMenuBar {
    background-color: #0d1117;
    color: #f0f6fc;
    border-bottom: 1px solid #30363d;
    padding: 4px 8px;
    font-size: 13px;
}

QMenuBar::item {
    background-color: transparent;
    color: #f0f6fc;
    padding: 6px 12px;
    border-radius: 4px;
    margin: 0px 2px;
}

QMenuBar::item:selected {
    background-color: #21262d;
    color: #f0f6fc;
}

QMenuBar::item:pressed {
    background-color: #30363d;
    color: #f0f6fc;
}

/* Menu Styling */
QMenu {
    background-color: #21262d;
    color: #f0f6fc;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 4px 0px;
    font-size: 13px;
}

QMenu::item {
    background-color: transparent;
    color: #f0f6fc;
    padding: 8px 24px 8px 32px;
    margin: 1px 4px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #30363d;
    color: #f0f6fc;
}

QMenu::item:disabled {
    color: #8b949e;
    background-color: transparent;
}

QMenu::separator {
    height: 1px;
    background-color: #30363d;
    margin: 4px 8px;
}

QMenu::indicator {
    width: 16px;
    height: 16px;
    left: 8px;
}

QMenu::indicator:checked {
    background-color: #238636;
    border: 1px solid #2ea043;
    border-radius: 3px;
}

QMenu::indicator:unchecked {
    background-color: transparent;
    border: 1px solid #30363d;
    border-radius: 3px;
}
"""

# Window-specific styles
MAIN_WINDOW_STYLE = """
QMainWindow {
    background-color: #0d1117;
    color: #f0f6fc;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
}
"""

CENTRAL_WIDGET_STYLE = """
QWidget {
    background-color: #0d1117;
}
"""
